package com.zerosense.bluetooth

import android.Manifest
import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothDevice
import android.bluetooth.BluetoothServerSocket
import android.bluetooth.BluetoothSocket
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.pm.PackageManager
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.util.Log
import androidx.core.content.ContextCompat
import org.json.JSONArray
import org.json.JSONObject
import java.io.IOException
import java.util.*
import java.util.concurrent.ConcurrentHashMap

/**
 * 蓝牙管理器 - SDK 核心类
 */
class BluetoothManager private constructor(private val context: Context) {

    // 先定义handler，确保在init块中可以使用
    private val handler = Handler(Looper.getMainLooper())

    init {
        Log.d(TAG, "BluetoothManager 开始初始化...")
        try {
            // 延迟启动连接池清理定时器，避免初始化时的潜在问题
            handler.postDelayed({
                startConnectionCleanupTimer()
            }, 1000) // 1秒后启动

            Log.d(TAG, "BluetoothManager 初始化成功")
        } catch (e: Exception) {
            Log.e(TAG, "BluetoothManager 初始化失败: ${e.message}", e)
            throw e
        }
    }

    companion object {
        private const val TAG = "BluetoothManager"

        @Volatile
        private var INSTANCE: BluetoothManager? = null

        /**
         * 获取单例实例
         */
        fun getInstance(context: Context): BluetoothManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: BluetoothManager(context.applicationContext).also { INSTANCE = it }
            }
        }

        // 标准 SPP UUID
        private val SPP_UUID = UUID.fromString("00001101-0000-1000-8000-00805F9B34FB")
        
        // 扫描超时时间（毫秒）
        private const val SCAN_TIMEOUT = 12000L

        // 连接超时时间（毫秒）- 给用户足够时间确认配对
        private const val CONNECT_TIMEOUT = 30000L

        // 已配对设备连接超时时间（毫秒）- 已配对设备连接更快
        private const val PAIRED_DEVICE_CONNECT_TIMEOUT = 20000L
    }

    private var bluetoothAdapter: BluetoothAdapter? = null
    private val callbacks = mutableSetOf<BluetoothCallback>()
    private val discoveredDevices = ConcurrentHashMap<String, BluetoothDeviceInfo>()
    private val connectedDevices = ConcurrentHashMap<String, BluetoothSocket>()
    private val connectingDevices = ConcurrentHashMap<String, Boolean>() // 正在连接的设备

    // 持久连接池 - 用于高效数据传输
    private val connectionPool = ConcurrentHashMap<String, BluetoothSocket>()
    private val connectionLastUsed = ConcurrentHashMap<String, Long>()
    private val CONNECTION_TIMEOUT = 30000L // 30秒无活动后关闭连接
    
    private var currentScanState = BluetoothScanState.IDLE
    private var scanTimeoutRunnable: Runnable? = null
    private var isReceiverRegistered = false

    // 连接池清理定时器
    private var connectionCleanupRunnable: Runnable? = null

    // 蓝牙服务器相关
    private var bluetoothServerSocket: BluetoothServerSocket? = null
    private var serverThread: Thread? = null
    private var isServerRunning = false

    // BLE管理器 - 延迟初始化
    private var bleManager: BleManager? = null
    
    // 蓝牙广播接收器
    private val bluetoothReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            when (intent?.action) {
                BluetoothDevice.ACTION_FOUND -> {
                    val device = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                        intent.getParcelableExtra(BluetoothDevice.EXTRA_DEVICE, BluetoothDevice::class.java)
                    } else {
                        @Suppress("DEPRECATION")
                        intent.getParcelableExtra(BluetoothDevice.EXTRA_DEVICE)
                    }
                    val rssi = intent.getShortExtra(BluetoothDevice.EXTRA_RSSI, Short.MIN_VALUE).toInt()
                    
                    device?.let {
                        val deviceInfo = BluetoothDeviceInfo.fromBluetoothDevice(it, rssi)
                        discoveredDevices[it.address] = deviceInfo
                        notifyDeviceFound(deviceInfo)
                    }
                }
                
                BluetoothAdapter.ACTION_DISCOVERY_STARTED -> {
                    updateScanState(BluetoothScanState.SCANNING)
                }
                
                BluetoothAdapter.ACTION_DISCOVERY_FINISHED -> {
                    updateScanState(BluetoothScanState.SCAN_FINISHED)
                    stopScanTimeout()
                }

                BluetoothDevice.ACTION_BOND_STATE_CHANGED -> {
                    val device = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                        intent.getParcelableExtra(BluetoothDevice.EXTRA_DEVICE, BluetoothDevice::class.java)
                    } else {
                        @Suppress("DEPRECATION")
                        intent.getParcelableExtra(BluetoothDevice.EXTRA_DEVICE)
                    }
                    val bondState = intent.getIntExtra(BluetoothDevice.EXTRA_BOND_STATE, BluetoothDevice.BOND_NONE)
                    val previousBondState = intent.getIntExtra(BluetoothDevice.EXTRA_PREVIOUS_BOND_STATE, BluetoothDevice.BOND_NONE)

                    device?.let {
                        when (bondState) {
                            BluetoothDevice.BOND_BONDED -> {
                                if (previousBondState == BluetoothDevice.BOND_BONDING) {
                                    // 配对刚刚成功，检查是否有正在连接的请求
                                    if (connectingDevices.containsKey(it.address)) {
                                        handler.post {
                                            notifyError("配对成功！正在建立连接...")
                                        }
                                        // 使用专门的配对后连接方法
                                        connectAfterPairing(it.address)
                                    }
                                }
                            }
                            BluetoothDevice.BOND_NONE -> {
                                if (previousBondState == BluetoothDevice.BOND_BONDING) {
                                    // 配对失败
                                    connectingDevices.remove(it.address)
                                    handler.post {
                                        notifyError("配对失败，请重试")
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    
    /**
     * 添加回调监听器
     */
    fun addCallback(callback: BluetoothCallback) {
        callbacks.add(callback)
    }
    
    /**
     * 移除回调监听器
     */
    fun removeCallback(callback: BluetoothCallback) {
        callbacks.remove(callback)
    }
    
    /**
     * 初始化蓝牙适配器（如果尚未初始化）
     */
    private fun initializeBluetoothAdapter() {
        if (bluetoothAdapter == null) {
            try {
                // 直接获取蓝牙适配器，不使用阻塞等待
                bluetoothAdapter = BluetoothAdapter.getDefaultAdapter()
                if (bluetoothAdapter != null) {
                    Log.d(TAG, "蓝牙适配器初始化成功")
                } else {
                    Log.w(TAG, "蓝牙适配器获取为null - 设备可能不支持蓝牙")
                }
            } catch (e: Exception) {
                Log.e(TAG, "蓝牙适配器初始化异常: ${e.message}", e)
                bluetoothAdapter = null
            }
        }
    }

    /**
     * 检查蓝牙是否可用
     */
    fun isBluetoothAvailable(): Boolean {
        initializeBluetoothAdapter()
        val available = bluetoothAdapter != null
        Log.d(TAG, "蓝牙可用性检查: $available")
        return available
    }
    
    /**
     * 检查蓝牙是否已启用
     */
    fun isBluetoothEnabled(): Boolean {
        initializeBluetoothAdapter()
        val enabled = bluetoothAdapter?.isEnabled == true
        Log.d(TAG, "蓝牙启用状态检查: $enabled")
        return enabled
    }
    
    /**
     * 检查是否有必要的权限
     */
    fun hasRequiredPermissions(): Boolean {
        val permissions = getRequiredPermissions()
        return permissions.all { permission ->
            ContextCompat.checkSelfPermission(context, permission) == PackageManager.PERMISSION_GRANTED
        }
    }
    
    /**
     * 获取需要的权限列表
     */
    fun getRequiredPermissions(): Array<String> {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            arrayOf(
                Manifest.permission.BLUETOOTH_SCAN,
                Manifest.permission.BLUETOOTH_CONNECT,
                Manifest.permission.ACCESS_FINE_LOCATION
            )
        } else {
            arrayOf(
                Manifest.permission.BLUETOOTH,
                Manifest.permission.BLUETOOTH_ADMIN,
                Manifest.permission.ACCESS_FINE_LOCATION
            )
        }
    }
    
    /**
     * 开始扫描蓝牙设备
     */
    fun startScan(): Boolean {
        if (!isBluetoothAvailable()) {
            notifyError("蓝牙不可用")
            return false
        }
        
        if (!isBluetoothEnabled()) {
            notifyError("蓝牙未启用")
            return false
        }
        
        if (!hasRequiredPermissions()) {
            notifyPermissionRequired(getRequiredPermissions())
            return false
        }
        
        if (currentScanState == BluetoothScanState.SCANNING) {
            return true // 已在扫描中
        }
        
        // 清空之前的发现设备
        discoveredDevices.clear()
        
        // 注册广播接收器
        registerReceiver()
        
        // 开始扫描
        val started = bluetoothAdapter?.startDiscovery() == true
        if (started) {
            startScanTimeout()
        } else {
            notifyError("启动扫描失败")
            unregisterReceiver()
        }
        
        return started
    }
    
    /**
     * 停止扫描蓝牙设备
     */
    fun stopScan() {
        bluetoothAdapter?.cancelDiscovery()
        stopScanTimeout()
        unregisterReceiver()
        updateScanState(BluetoothScanState.SCAN_FINISHED)
    }
    
    /**
     * 获取已发现的设备列表
     */
    fun getDiscoveredDevices(): List<BluetoothDeviceInfo> {
        return discoveredDevices.values.toList()
    }
    
    /**
     * 获取已配对的设备列表
     */
    fun getPairedDevices(): List<BluetoothDeviceInfo> {
        initializeBluetoothAdapter()

        if (!hasRequiredPermissions()) {
            notifyPermissionRequired(getRequiredPermissions())
            return emptyList()
        }

        return bluetoothAdapter?.bondedDevices?.map { device ->
            BluetoothDeviceInfo.fromBluetoothDevice(device).copy(isPaired = true)
        } ?: emptyList()
    }

    /**
     * 发送数据到指定设备（高效版本 - 使用连接池）
     */
    fun sendData(deviceAddress: String, data: String): Boolean {
        println("📤 开始发送数据流程")
        println("📤 目标设备地址: $deviceAddress")
        println("📤 发送的数据: '$data'")
        println("📤 数据长度: ${data.length} 字节")

        initializeBluetoothAdapter()

        if (!hasRequiredPermissions()) {
            println("❌ 缺少蓝牙权限")
            notifyPermissionRequired(getRequiredPermissions())
            return false
        }

        val device = bluetoothAdapter?.getRemoteDevice(deviceAddress)
        if (device == null) {
            println("❌ 设备不存在: $deviceAddress")
            notifyError("设备不存在: $deviceAddress")
            return false
        }

        println("📤 找到设备: ${device.name ?: "Unknown"} (${device.address})")

        // 检查设备是否已配对
        if (device.bondState != BluetoothDevice.BOND_BONDED) {
            println("❌ 设备未配对: ${device.bondState}")
            notifyError("设备未配对，请先配对设备")
            return false
        }

        println("📤 设备已配对，准备发送数据")

        // 在后台线程中发送数据（使用连接池）
        Thread {
            sendDataFast(device, data)
        }.start()

        println("📤 数据发送线程已启动")
        return true
    }

    /**
     * 高效数据发送方法（使用连接池）
     */
    private fun sendDataFast(device: BluetoothDevice, data: String) {
        val deviceAddress = device.address
        val startTime = System.currentTimeMillis()

        println("📤 [sendDataFast] 开始发送数据到: ${device.name ?: "Unknown"} ($deviceAddress)")
        println("📤 [sendDataFast] 数据内容: '$data'")

        try {
            // 获取或创建连接
            println("📤 [sendDataFast] 获取或创建连接...")
            val socket = getOrCreateConnection(device)
            if (socket == null) {
                println("❌ [sendDataFast] 无法建立连接")
                handler.post {
                    notifyError("无法建立连接到设备: ${device.name ?: deviceAddress}")
                }
                return
            }

            println("📤 [sendDataFast] 连接已建立，准备发送数据")
            println("📤 [sendDataFast] Socket状态: 连接=${socket.isConnected}")

            // 发送数据
            val outputStream = socket.outputStream
            val dataBytes = data.toByteArray(Charsets.UTF_8)

            println("📤 [sendDataFast] 数据字节长度: ${dataBytes.size}")
            println("📤 [sendDataFast] 数据字节: ${dataBytes.joinToString(" ") { "%02X".format(it) }}")

            outputStream.write(dataBytes)
            outputStream.flush()

            println("📤 [sendDataFast] 数据写入完成并刷新输出流")

            // 更新最后使用时间
            connectionLastUsed[deviceAddress] = System.currentTimeMillis()

            val sendTime = System.currentTimeMillis() - startTime
            println("📤 [sendDataFast] 发送完成，耗时: ${sendTime}ms")

            handler.post {
                notifySuccess("✅ 数据发送成功 (${sendTime}ms): \"$data\"")
            }

            // 可选：读取响应（使用非阻塞方式避免卡住）
            try {
                val inputStream = socket.inputStream
                val available = inputStream.available()
                println("📤 [sendDataFast] 检查响应数据，可用字节: $available")

                if (available > 0) {
                    val buffer = ByteArray(1024)
                    val bytesRead = inputStream.read(buffer)
                    if (bytesRead > 0) {
                        val response = String(buffer, 0, bytesRead, Charsets.UTF_8)
                        println("📨 [sendDataFast] 收到响应: '$response'")
                        handler.post {
                            notifyInfo("📨 收到响应: \"$response\"")
                        }
                    }
                } else {
                    println("📤 [sendDataFast] 暂无响应数据")
                }
            } catch (e: Exception) {
                println("📤 [sendDataFast] 读取响应异常（正常）: ${e.message}")
            }

        } catch (e: Exception) {
            handler.post {
                notifyError("数据发送失败: ${e.message}")
            }
            // 连接可能已断开，从连接池中移除
            removeFromConnectionPool(deviceAddress)
        }
    }

    /**
     * 获取或创建到设备的连接
     */
    private fun getOrCreateConnection(device: BluetoothDevice): BluetoothSocket? {
        val deviceAddress = device.address

        // 检查连接池中是否有可用连接
        val existingSocket = connectionPool[deviceAddress]
        if (existingSocket != null && existingSocket.isConnected) {
            // 连接仍然有效，直接使用
            return existingSocket
        }

        // 需要创建新连接
        return createNewConnection(device)
    }

    /**
     * 创建新的连接并加入连接池（使用可靠的多重连接方法）
     */
    private fun createNewConnection(device: BluetoothDevice): BluetoothSocket? {
        val deviceAddress = device.address

        try {
            handler.post {
                notifyInfo("建立新连接到: ${device.name ?: deviceAddress}")
            }

            // 停止扫描以提高连接成功率
            bluetoothAdapter?.cancelDiscovery()

            // 使用可靠的多重连接方法
            val socket = createBluetoothSocket(device, 0, false)
            if (socket == null) {
                handler.post {
                    notifyError("无法创建蓝牙Socket")
                }
                return null
            }

            // 建立连接（设置较短的超时时间用于快速发送）
            socket.connect()

            // 加入连接池
            connectionPool[deviceAddress] = socket
            connectionLastUsed[deviceAddress] = System.currentTimeMillis()

            handler.post {
                notifySuccess("连接建立成功: ${device.name ?: deviceAddress}")
            }

            return socket

        } catch (e: Exception) {
            handler.post {
                notifyError("建立连接失败: ${e.message}")
            }
            return null
        }
    }

    /**
     * 从连接池中移除连接
     */
    private fun removeFromConnectionPool(deviceAddress: String) {
        val socket = connectionPool.remove(deviceAddress)
        connectionLastUsed.remove(deviceAddress)

        try {
            socket?.close()
        } catch (e: Exception) {
            // 忽略关闭异常
        }
    }

    /**
     * 清理过期的连接
     */
    private fun cleanupExpiredConnections() {
        val currentTime = System.currentTimeMillis()
        val expiredDevices = mutableListOf<String>()

        connectionLastUsed.forEach { (deviceAddress, lastUsed) ->
            if (currentTime - lastUsed > CONNECTION_TIMEOUT) {
                expiredDevices.add(deviceAddress)
            }
        }

        expiredDevices.forEach { deviceAddress ->
            removeFromConnectionPool(deviceAddress)
        }
    }

    /**
     * 启动连接池清理定时器
     */
    private fun startConnectionCleanupTimer() {
        connectionCleanupRunnable = object : Runnable {
            override fun run() {
                cleanupExpiredConnections()
                // 每10秒检查一次过期连接
                handler.postDelayed(this, 10000)
            }
        }
        handler.postDelayed(connectionCleanupRunnable!!, 10000)
    }

    /**
     * 停止连接池清理定时器
     */
    private fun stopConnectionCleanupTimer() {
        connectionCleanupRunnable?.let { runnable ->
            handler.removeCallbacks(runnable)
            connectionCleanupRunnable = null
        }
    }

    /**
     * 关闭所有连接池连接（用于应用退出时清理）
     */
    fun closeAllConnections() {
        stopConnectionCleanupTimer()

        connectionPool.values.forEach { socket ->
            try {
                socket.close()
            } catch (e: Exception) {
                // 忽略关闭异常
            }
        }

        connectionPool.clear()
        connectionLastUsed.clear()
    }

    /**
     * 配对成功后的处理（现在只是更新状态）
     */
    fun connectAfterPairing(deviceAddress: String): Boolean {
        val device = bluetoothAdapter?.getRemoteDevice(deviceAddress) ?: return false
        val deviceInfo = BluetoothDeviceInfo(
            name = device.name ?: "未知设备",
            address = device.address,
            isConnected = true, // 配对完成即视为可用
            isPaired = true
        )

        // 清除配对状态
        connectingDevices.remove(deviceAddress)

        // 更新状态为已连接（实际是已配对可用）
        handler.post {
            notifyConnectionStateChanged(deviceInfo, BluetoothConnectionState.CONNECTED)
            notifyError("配对成功！现在可以发送数据了")
        }

        return true
    }

    /**
     * 连接到指定设备（实际上是触发配对过程）
     */
    fun connectToDevice(deviceAddress: String): Boolean {
        initializeBluetoothAdapter()

        if (!hasRequiredPermissions()) {
            notifyPermissionRequired(getRequiredPermissions())
            return false
        }

        val device = bluetoothAdapter?.getRemoteDevice(deviceAddress)
        if (device == null) {
            notifyError("设备不存在: $deviceAddress")
            return false
        }

        // 检查设备是否已配对
        if (device.bondState == BluetoothDevice.BOND_BONDED) {
            notifyError("设备已配对，可以直接发送数据")
            val deviceInfo = BluetoothDeviceInfo.fromBluetoothDevice(device)
            notifyConnectionStateChanged(deviceInfo, BluetoothConnectionState.CONNECTED)
            return true
        }

        // 如果正在配对，不重复操作
        if (connectingDevices.containsKey(deviceAddress)) {
            return true
        }

        val deviceInfo = BluetoothDeviceInfo.fromBluetoothDevice(device)
        notifyConnectionStateChanged(deviceInfo, BluetoothConnectionState.CONNECTING)

        // 标记为正在配对
        connectingDevices[deviceAddress] = true

        // 开始配对过程
        notifyError("开始配对设备，请在目标设备上确认配对请求...")
        val paired = device.createBond()

        if (!paired) {
            connectingDevices.remove(deviceAddress)
            notifyError("无法启动配对过程")
            notifyConnectionStateChanged(deviceInfo, BluetoothConnectionState.DISCONNECTED)
            return false
        }

        return true
    }

    /**
     * 内部连接方法，支持重试和多种连接方式
     */
    private fun connectToDeviceInternal(device: BluetoothDevice, deviceInfo: BluetoothDeviceInfo, retryCount: Int) {
        val maxRetries = 1 // 减少重试次数，给用户更多时间
        val isPaired = device.bondState == BluetoothDevice.BOND_BONDED
        // 检查设备当前的配对状态（可能在连接过程中发生了变化）
        val currentlyPaired = device.bondState == BluetoothDevice.BOND_BONDED

        try {
            // 停止扫描以提高连接成功率
            bluetoothAdapter?.cancelDiscovery()

            // 等待一下让扫描完全停止
            Thread.sleep(800)

            // 检测设备支持的服务
            detectDeviceServices(device)

            if (!isPaired && currentlyPaired) {
                // 设备刚刚配对成功，等待更长时间让配对过程完全完成
                handler.post {
                    notifyError("配对成功！正在建立连接...")
                }
                Thread.sleep(2000) // 等待2秒让配对过程稳定
            }

            // 尝试多种连接方法
            val socket = createBluetoothSocket(device, retryCount, !isPaired && currentlyPaired)

            if (socket == null) {
                handler.post {
                    notifyError("无法创建蓝牙Socket")
                }
                connectingDevices.remove(device.address)
                return
            }

            // 根据设备配对状态设置不同的超时时间（使用当前状态）
            val timeout = if (currentlyPaired) PAIRED_DEVICE_CONNECT_TIMEOUT else CONNECT_TIMEOUT

            handler.post {
                notifyError("开始连接，超时时间: ${timeout/1000}秒")
            }

            // 设置连接超时
            val timeoutRunnable = Runnable {
                try {
                    socket.close()
                } catch (e: IOException) {
                    // 忽略关闭异常
                }
                handler.post {
                    if (retryCount < maxRetries) {
                        // 提示用户正在重试
                        notifyError("连接超时，正在重试...（${retryCount + 1}/${maxRetries + 1}）")
                        // 重试连接
                        Thread {
                            Thread.sleep(3000) // 等待3秒后重试，给用户更多时间
                            connectToDeviceInternal(device, deviceInfo, retryCount + 1)
                        }.start()
                    } else {
                        notifyConnectionStateChanged(deviceInfo, BluetoothConnectionState.CONNECT_TIMEOUT)
                        if (isPaired) {
                            notifyError("连接超时。设备可能不在范围内或已被其他设备连接")
                        } else {
                            notifyError("连接超时。请确保在设备上确认配对请求，或检查设备是否支持连接")
                        }
                    }
                }
            }
            handler.postDelayed(timeoutRunnable, timeout)

            // 提供用户反馈
            if (retryCount == 0 && !currentlyPaired) {
                handler.post {
                    notifyError("正在连接设备，请在目标设备上确认配对请求...")
                }
            } else if (retryCount == 0 && currentlyPaired) {
                handler.post {
                    notifyError("正在连接已配对设备...")
                }
            }

            // 执行连接
            handler.post {
                notifyError("正在建立Socket连接...")
            }

            val startTime = System.currentTimeMillis()
            socket.connect()
            val connectTime = System.currentTimeMillis() - startTime

            // 连接成功，取消超时
            handler.removeCallbacks(timeoutRunnable)

            handler.post {
                notifyError("Socket连接成功，耗时: ${connectTime}ms")
            }

            // 保存连接
            connectedDevices[device.address] = socket
            // 清除连接状态
            connectingDevices.remove(device.address)

            handler.post {
                notifyConnectionStateChanged(
                    deviceInfo.copy(isConnected = true),
                    BluetoothConnectionState.CONNECTED
                )
                if (retryCount > 0) {
                    notifyError("连接成功！（第 ${retryCount + 1} 次尝试）")
                } else {
                    notifyError("连接成功！")
                }
            }

        } catch (e: IOException) {
            handler.post {
                if (retryCount < maxRetries) {
                    // 提示用户正在重试
                    notifyError("连接失败，正在重试...（${retryCount + 1}/${maxRetries + 1}）")

                    // 在重试前尝试重置蓝牙连接状态
                    if (retryCount == 0 && currentlyPaired) {
                        notifyError("尝试重置设备连接状态...")
                        Thread {
                            try {
                                // 尝试清除设备缓存（需要反射调用）
                                val refreshMethod = device.javaClass.getMethod("refresh")
                                refreshMethod.invoke(device)
                                Thread.sleep(2000)
                                handler.post {
                                    notifyError("设备状态重置完成，开始重试连接...")
                                }
                            } catch (ex: Exception) {
                                handler.post {
                                    notifyError("设备状态重置失败，直接重试连接...")
                                }
                            }
                            Thread.sleep(3000) // 等待3秒后重试
                            connectToDeviceInternal(device, deviceInfo, retryCount + 1)
                        }.start()
                    } else {
                        // 重试连接
                        Thread {
                            Thread.sleep(3000) // 等待3秒后重试
                            connectToDeviceInternal(device, deviceInfo, retryCount + 1)
                        }.start()
                    }
                } else {
                    // 清除连接状态
                    connectingDevices.remove(device.address)
                    notifyConnectionStateChanged(deviceInfo, BluetoothConnectionState.CONNECT_FAILED)

                    // 提供更详细的错误信息
                    val errorMsg = when {
                        e.message?.contains("read failed") == true -> {
                            if (currentlyPaired) {
                                "连接失败：设备可能不在范围内、电量不足或正被其他设备使用。请确保设备处于可连接状态。"
                            } else {
                                "连接失败：配对可能未完成或设备不支持此连接方式。请重新尝试配对。"
                            }
                        }
                        e.message?.contains("timeout") == true -> {
                            "连接超时：设备响应时间过长，请检查设备状态并重试。"
                        }
                        e.message?.contains("Service discovery failed") == true -> {
                            "服务发现失败：设备可能不支持所需的蓝牙服务。"
                        }
                        else -> "连接失败: ${e.message}"
                    }
                    notifyError(errorMsg)
                }
            }
        }
    }

    /**
     * 取消正在进行的连接
     */
    fun cancelConnection(deviceAddress: String): Boolean {
        if (!connectingDevices.containsKey(deviceAddress)) {
            return false // 没有正在连接的设备
        }

        connectingDevices.remove(deviceAddress)

        val device = bluetoothAdapter?.getRemoteDevice(deviceAddress)
        val deviceInfo = device?.let { BluetoothDeviceInfo.fromBluetoothDevice(it) }

        deviceInfo?.let {
            notifyConnectionStateChanged(it, BluetoothConnectionState.DISCONNECTED)
            notifyError("连接已取消")
        }

        return true
    }

    /**
     * 断开与指定设备的连接
     */
    fun disconnectFromDevice(deviceAddress: String): Boolean {
        val socket = connectedDevices[deviceAddress]
        if (socket == null) {
            return false // 设备未连接
        }

        val device = bluetoothAdapter?.getRemoteDevice(deviceAddress)
        val deviceInfo = device?.let { BluetoothDeviceInfo.fromBluetoothDevice(it) }

        deviceInfo?.let {
            notifyConnectionStateChanged(it, BluetoothConnectionState.DISCONNECTING)
        }

        Thread {
            try {
                socket.close()
                connectedDevices.remove(deviceAddress)

                handler.post {
                    deviceInfo?.let {
                        notifyConnectionStateChanged(
                            it.copy(isConnected = false),
                            BluetoothConnectionState.DISCONNECTED
                        )
                    }
                }
            } catch (e: IOException) {
                handler.post {
                    notifyError("断开连接失败: ${e.message}")
                }
            }
        }.start()

        return true
    }

    /**
     * 断开所有连接
     */
    fun disconnectAll() {
        val addresses = connectedDevices.keys.toList()
        addresses.forEach { address ->
            disconnectFromDevice(address)
        }
    }

    /**
     * 获取已连接的设备列表
     */
    fun getConnectedDevices(): List<BluetoothDeviceInfo> {
        return connectedDevices.keys.mapNotNull { address ->
            bluetoothAdapter?.getRemoteDevice(address)?.let { device ->
                BluetoothDeviceInfo.fromBluetoothDevice(device).copy(isConnected = true)
            }
        }
    }

    /**
     * 检查设备是否已连接
     */
    fun isDeviceConnected(deviceAddress: String): Boolean {
        return connectedDevices.containsKey(deviceAddress)
    }

    /**
     * 静默连接已配对的设备
     * 自动尝试连接所有已配对的设备
     */
    fun connectToPairedDevicesAutomatically(): Boolean {
        if (!hasRequiredPermissions()) {
            notifyPermissionRequired(getRequiredPermissions())
            return false
        }

        val pairedDevices = getPairedDevices()
        if (pairedDevices.isEmpty()) {
            notifyError("没有已配对的设备")
            return false
        }

        var hasStartedConnection = false
        pairedDevices.forEach { deviceInfo ->
            if (!isDeviceConnected(deviceInfo.address)) {
                connectToDevice(deviceInfo.address)
                hasStartedConnection = true
            }
        }

        return hasStartedConnection
    }

    /**
     * 静默连接指定的已配对设备
     */
    fun connectToPairedDeviceAutomatically(deviceAddress: String): Boolean {
        if (!hasRequiredPermissions()) {
            notifyPermissionRequired(getRequiredPermissions())
            return false
        }

        val pairedDevices = getPairedDevices()
        val targetDevice = pairedDevices.find { it.address == deviceAddress }

        if (targetDevice == null) {
            notifyError("设备未配对: $deviceAddress")
            return false
        }

        if (isDeviceConnected(deviceAddress)) {
            return true // 已连接
        }

        return connectToPairedDevice(deviceAddress)
    }

    /**
     * 连接已配对设备的专用方法
     * 对已配对设备使用更可靠的连接方式
     */
    fun connectToPairedDevice(deviceAddress: String): Boolean {
        if (!hasRequiredPermissions()) {
            notifyPermissionRequired(getRequiredPermissions())
            return false
        }

        val device = bluetoothAdapter?.getRemoteDevice(deviceAddress)
        if (device == null) {
            notifyError("设备不存在: $deviceAddress")
            return false
        }

        // 检查设备是否已配对
        if (device.bondState != BluetoothDevice.BOND_BONDED) {
            notifyError("设备未配对: $deviceAddress")
            return false
        }

        // 如果已经连接，直接返回成功
        if (connectedDevices.containsKey(deviceAddress)) {
            return true
        }

        val deviceInfo = BluetoothDeviceInfo.fromBluetoothDevice(device)
        notifyConnectionStateChanged(deviceInfo, BluetoothConnectionState.CONNECTING)

        // 在后台线程中执行连接
        Thread {
            connectToPairedDeviceInternal(device, deviceInfo, 0)
        }.start()

        return true
    }

    /**
     * 已配对设备的内部连接方法
     */
    private fun connectToPairedDeviceInternal(device: BluetoothDevice, deviceInfo: BluetoothDeviceInfo, retryCount: Int) {
        val maxRetries = 1 // 已配对设备减少重试次数

        try {
            // 停止扫描以提高连接成功率
            bluetoothAdapter?.cancelDiscovery()

            // 等待一下让扫描完全停止
            Thread.sleep(500)

            // 对于已配对设备，根据重试次数选择连接方法
            val socket = if (retryCount == 0) {
                // 第一次尝试：使用反射方法（对已配对设备更可靠）
                try {
                    val method = device.javaClass.getMethod("createRfcommSocket", Int::class.javaPrimitiveType)
                    method.invoke(device, 1) as BluetoothSocket
                } catch (e: Exception) {
                    // 如果反射失败，使用标准方法
                    device.createRfcommSocketToServiceRecord(SPP_UUID)
                }
            } else {
                // 重试时使用标准方法
                device.createRfcommSocketToServiceRecord(SPP_UUID)
            }

            // 设置连接超时
            val timeoutRunnable = Runnable {
                try {
                    socket.close()
                } catch (e: IOException) {
                    // 忽略关闭异常
                }
                handler.post {
                    if (retryCount < maxRetries) {
                        // 提示用户正在重试
                        notifyError("连接超时，正在重试...（${retryCount + 1}/${maxRetries + 1}）")
                        // 重试连接
                        Thread {
                            Thread.sleep(2000) // 等待2秒后重试
                            connectToPairedDeviceInternal(device, deviceInfo, retryCount + 1)
                        }.start()
                    } else {
                        notifyConnectionStateChanged(deviceInfo, BluetoothConnectionState.CONNECT_TIMEOUT)
                        notifyError("连接超时。设备可能不在范围内、电量不足或已被其他设备连接")
                    }
                }
            }
            handler.postDelayed(timeoutRunnable, PAIRED_DEVICE_CONNECT_TIMEOUT)

            // 执行连接
            socket.connect()

            // 连接成功，取消超时
            handler.removeCallbacks(timeoutRunnable)

            // 保存连接
            connectedDevices[device.address] = socket

            handler.post {
                notifyConnectionStateChanged(
                    deviceInfo.copy(isConnected = true),
                    BluetoothConnectionState.CONNECTED
                )
                if (retryCount > 0) {
                    notifyError("连接成功！（第 ${retryCount + 1} 次尝试）")
                } else {
                    notifyError("连接成功！")
                }
            }

        } catch (e: IOException) {
            handler.post {
                if (retryCount < maxRetries) {
                    // 提示用户正在重试
                    notifyError("连接失败，正在重试...（${retryCount + 1}/${maxRetries + 1}）")
                    // 重试连接
                    Thread {
                        Thread.sleep(2000) // 等待2秒后重试
                        connectToPairedDeviceInternal(device, deviceInfo, retryCount + 1)
                    }.start()
                } else {
                    notifyConnectionStateChanged(deviceInfo, BluetoothConnectionState.CONNECT_FAILED)
                    notifyError("连接失败: ${e.message}")
                }
            }
        }
    }

    /**
     * 释放资源
     */
    fun release() {
        stopScan()
        // 只有在BLE管理器已初始化时才清理
        bleManager?.let {
            it.stopBleScan()
            it.cleanup()
        }
        disconnectAll()
        callbacks.clear()
        INSTANCE = null
    }

    /**
     * 检测设备支持的服务
     */
    private fun detectDeviceServices(device: BluetoothDevice) {
        try {
            val uuids = device.uuids
            if (uuids != null && uuids.isNotEmpty()) {
                handler.post {
                    notifyError("设备支持的服务: ${uuids.joinToString { it.toString() }}")
                }
            } else {
                handler.post {
                    notifyError("无法获取设备服务信息，使用默认连接方法")
                }
            }
        } catch (e: Exception) {
            handler.post {
                notifyError("检测设备服务时出错: ${e.message}")
            }
        }
    }

    /**
     * 创建蓝牙Socket，尝试多种连接方法
     */
    private fun createBluetoothSocket(device: BluetoothDevice, retryCount: Int, isJustPaired: Boolean): BluetoothSocket? {
        val connectionMethods = listOf(
            "标准SPP方法" to { device.createRfcommSocketToServiceRecord(SPP_UUID) },
            "反射方法(端口1)" to {
                val method = device.javaClass.getMethod("createRfcommSocket", Int::class.javaPrimitiveType)
                method.invoke(device, 1) as BluetoothSocket
            },
            "反射方法(端口2)" to {
                val method = device.javaClass.getMethod("createRfcommSocket", Int::class.javaPrimitiveType)
                method.invoke(device, 2) as BluetoothSocket
            },
            "反射方法(端口3)" to {
                val method = device.javaClass.getMethod("createRfcommSocket", Int::class.javaPrimitiveType)
                method.invoke(device, 3) as BluetoothSocket
            }
        )

        // 根据情况选择优先的连接方法
        val orderedMethods = when {
            isJustPaired -> {
                // 刚配对的设备，优先使用反射方法
                listOf(1, 0, 2, 3)
            }
            retryCount == 0 -> {
                // 第一次尝试，使用标准方法
                listOf(0, 1, 2, 3)
            }
            else -> {
                // 重试时，优先使用反射方法
                listOf(1, 2, 3, 0)
            }
        }

        for (methodIndex in orderedMethods) {
            try {
                val (methodName, createSocket) = connectionMethods[methodIndex]
                handler.post {
                    notifyError("尝试连接方法: $methodName")
                }
                val socket = createSocket()
                if (socket != null) {
                    handler.post {
                        notifyError("成功创建Socket: $methodName")
                    }
                    return socket
                }
            } catch (e: Exception) {
                val (methodName, _) = connectionMethods[methodIndex]
                handler.post {
                    notifyError("连接方法失败: $methodName - ${e.message}")
                }
                // 继续尝试下一种方法
            }
        }

        // 所有方法都失败了
        handler.post {
            notifyError("所有连接方法都失败了")
        }
        return null
    }

    // 私有方法
    private fun registerReceiver() {
        if (!isReceiverRegistered) {
            val filter = IntentFilter().apply {
                addAction(BluetoothDevice.ACTION_FOUND)
                addAction(BluetoothAdapter.ACTION_DISCOVERY_STARTED)
                addAction(BluetoothAdapter.ACTION_DISCOVERY_FINISHED)
                addAction(BluetoothDevice.ACTION_BOND_STATE_CHANGED) // 添加配对状态监听
            }
            context.registerReceiver(bluetoothReceiver, filter)
            isReceiverRegistered = true
        }
    }

    private fun unregisterReceiver() {
        if (isReceiverRegistered) {
            try {
                context.unregisterReceiver(bluetoothReceiver)
            } catch (e: IllegalArgumentException) {
                // 接收器可能已经被注销
            }
            isReceiverRegistered = false
        }
    }

    private fun startScanTimeout() {
        stopScanTimeout()
        scanTimeoutRunnable = Runnable {
            bluetoothAdapter?.cancelDiscovery()
            updateScanState(BluetoothScanState.SCAN_FINISHED)
            unregisterReceiver()
        }
        handler.postDelayed(scanTimeoutRunnable!!, SCAN_TIMEOUT)
    }

    private fun stopScanTimeout() {
        scanTimeoutRunnable?.let { runnable ->
            handler.removeCallbacks(runnable)
            scanTimeoutRunnable = null
        }
    }

    private fun updateScanState(state: BluetoothScanState) {
        currentScanState = state
        notifyScanStateChanged(state)
    }

    private fun notifyScanStateChanged(state: BluetoothScanState) {
        callbacks.forEach { it.onScanStateChanged(state) }
    }

    private fun notifyDeviceFound(device: BluetoothDeviceInfo) {
        callbacks.forEach { it.onDeviceFound(device) }
    }

    private fun notifyConnectionStateChanged(device: BluetoothDeviceInfo, state: BluetoothConnectionState) {
        callbacks.forEach { it.onConnectionStateChanged(device, state) }
    }

    private fun notifyError(error: String) {
        callbacks.forEach { it.onError(error) }
    }

    private fun notifySuccess(message: String) {
        callbacks.forEach { it.onSuccess(message) }
    }

    private fun notifyInfo(message: String) {
        callbacks.forEach { it.onInfo(message) }
    }

    private fun notifyDataReceived(senderAddress: String, data: String) {
        callbacks.forEach { it.onDataReceived(senderAddress, data) }
    }

    // ==================== 蓝牙服务器功能 ====================

    /**
     * 启动统一蓝牙服务器，同时支持经典蓝牙和BLE消息接收
     * 这个方法会：
     * 1. 启动经典蓝牙服务器（SPP协议）
     * 2. 启动BLE扫描和连接管理
     * 3. 统一处理所有蓝牙消息
     */
    fun startUnifiedBluetoothServer(): Boolean {
        Log.d(TAG, "启动统一蓝牙服务器...")

        // 启动经典蓝牙服务器
        val classicResult = startBluetoothServer()

        // 启动BLE功能
        val bleResult = startBleServer()

        if (classicResult || bleResult) {
            notifySuccess("统一蓝牙服务器启动成功 - 经典蓝牙: $classicResult, BLE: $bleResult")
            return true
        } else {
            notifyError("统一蓝牙服务器启动失败")
            return false
        }
    }

    /**
     * 停止统一蓝牙服务器
     */
    fun stopUnifiedBluetoothServer() {
        Log.d(TAG, "停止统一蓝牙服务器...")
        stopBluetoothServer()
        stopBleServer()
        notifyInfo("统一蓝牙服务器已停止")
    }

    /**
     * 启动BLE服务器功能
     */
    private fun startBleServer(): Boolean {
        return try {
            // 启动BLE扫描以发现设备
            getBleManager().startBleScan()

            // 自动连接已配对的BLE设备（特别是点云灵动键）
            handler.postDelayed({
                autoConnectPairedBleDevices()
            }, 2000) // 等待2秒后开始自动连接

            Log.d(TAG, "BLE服务器功能启动成功")
            true
        } catch (e: Exception) {
            Log.e(TAG, "BLE服务器功能启动失败: ${e.message}", e)
            false
        }
    }

    /**
     * 停止BLE服务器功能
     */
    private fun stopBleServer() {
        try {
            getBleManager().stopBleScan()
            // 断开所有BLE连接
            val connectedDevices = getBleManager().getConnectedBleDevices()
            connectedDevices.forEach { deviceAddress ->
                getBleManager().disconnectBleDevice(deviceAddress)
            }
            Log.d(TAG, "BLE服务器功能已停止")
        } catch (e: Exception) {
            Log.e(TAG, "停止BLE服务器功能时出错: ${e.message}", e)
        }
    }

    /**
     * 自动连接已配对的BLE设备
     * 特别针对点云灵动键等按键设备
     */
    private fun autoConnectPairedBleDevices() {
        Log.d(TAG, "开始自动连接已配对的BLE设备...")

        try {
            val bluetoothAdapter = BluetoothAdapter.getDefaultAdapter()
            if (bluetoothAdapter == null) {
                Log.e(TAG, "蓝牙适配器不可用")
                return
            }

            val pairedDevices = bluetoothAdapter.bondedDevices
            var bleDeviceCount = 0

            pairedDevices?.forEach { device ->
                // BLE设备通常类型为DEVICE_TYPE_LE或DEVICE_TYPE_DUAL
                if (device.type == BluetoothDevice.DEVICE_TYPE_LE ||
                    device.type == BluetoothDevice.DEVICE_TYPE_DUAL) {

                    Log.d(TAG, "发现已配对的BLE设备: ${device.name} (${device.address})")

                    // 自动连接BLE设备
                    handler.postDelayed({
                        connectToBleDevice(device.address)
                    }, bleDeviceCount * 1000L) // 每个设备间隔1秒连接

                    bleDeviceCount++
                }
            }

            if (bleDeviceCount > 0) {
                Log.d(TAG, "找到 $bleDeviceCount 个已配对的BLE设备，开始自动连接")
                notifyInfo("找到 $bleDeviceCount 个已配对的BLE设备，正在自动连接...")
            } else {
                Log.d(TAG, "没有找到已配对的BLE设备")
                notifyInfo("没有找到已配对的BLE设备，请先配对点云灵动键")
            }

        } catch (e: Exception) {
            Log.e(TAG, "自动连接BLE设备失败: ${e.message}", e)
        }
    }

    /**
     * 启动蓝牙服务器，监听来自其他设备的连接
     */
    fun startBluetoothServer(): Boolean {
        initializeBluetoothAdapter()

        if (!hasRequiredPermissions()) {
            notifyPermissionRequired(getRequiredPermissions())
            return false
        }

        if (isServerRunning) {
            notifyInfo("蓝牙服务器已在运行")
            return false
        }

        try {
            bluetoothServerSocket = bluetoothAdapter?.listenUsingRfcommWithServiceRecord(
                "BluetoothChatServer",
                SPP_UUID
            )

            if (bluetoothServerSocket == null) {
                notifyError("无法创建蓝牙服务器Socket")
                return false
            }

            isServerRunning = true

            serverThread = Thread {
                acceptConnections()
            }.apply {
                name = "BluetoothServerThread"
                start()
            }

            // 服务器启动成功，不需要通过错误回调通知
            // UI会通过isServerRunning()状态来显示正确信息

            return true

        } catch (e: Exception) {
            handler.post {
                notifyError("启动蓝牙服务器失败: ${e.message}")
            }
            return false
        }
    }

    /**
     * 停止蓝牙服务器
     */
    fun stopBluetoothServer() {
        isServerRunning = false

        try {
            bluetoothServerSocket?.close()
            bluetoothServerSocket = null

            serverThread?.interrupt()
            serverThread = null

            handler.post {
                notifyInfo("蓝牙服务器已停止")
            }

        } catch (e: Exception) {
            handler.post {
                notifyError("停止蓝牙服务器时出错: ${e.message}")
            }
        }
    }

    /**
     * 检查服务器是否正在运行
     */
    fun isServerRunning(): Boolean {
        return isServerRunning
    }

    /**
     * 接受客户端连接的循环
     */
    private fun acceptConnections() {
        while (isServerRunning && bluetoothServerSocket != null) {
            try {
                val clientSocket = bluetoothServerSocket?.accept()

                if (clientSocket != null && isServerRunning) {
                    // 处理客户端连接
                    Thread {
                        handleClientConnection(clientSocket)
                    }.apply {
                        name = "ClientHandler-${clientSocket.remoteDevice.address}"
                        start()
                    }
                }

            } catch (e: Exception) {
                if (isServerRunning) {
                    handler.post {
                        notifyError("接受连接时出错: ${e.message}")
                    }
                }
                break
            }
        }
    }

    /**
     * 处理单个客户端连接
     */
    private fun handleClientConnection(socket: BluetoothSocket) {
        val device = socket.remoteDevice
        val deviceAddress = device.address

        handler.post {
            notifyError("客户端已连接: ${device.name ?: deviceAddress}")
        }

        try {
            val inputStream = socket.inputStream
            val buffer = ByteArray(1024)

            while (isServerRunning && socket.isConnected) {
                try {
                    val bytesRead = inputStream.read(buffer)
                    if (bytesRead > 0) {
                        val receivedData = String(buffer, 0, bytesRead, Charsets.UTF_8)

                        handler.post {
                            notifyInfo("收到来自 ${device.name ?: deviceAddress} 的数据: \"$receivedData\"")
                            notifyDataReceived(deviceAddress, receivedData)
                        }

                        // 处理树莓派控制指令
                        processRaspberryPiCommand(receivedData, socket)

                        // 可选：发送确认响应
                        try {
                            val outputStream = socket.outputStream
                            val response = "收到: $receivedData"
                            outputStream.write(response.toByteArray(Charsets.UTF_8))
                            outputStream.flush()
                        } catch (e: Exception) {
                            // 发送响应失败，但不影响接收
                        }
                    }
                } catch (e: Exception) {
                    // 读取数据失败，可能是连接断开
                    break
                }
            }

        } catch (e: Exception) {
            handler.post {
                notifyError("处理客户端连接时出错: ${e.message}")
            }
        } finally {
            try {
                socket.close()
                handler.post {
                    notifyError("客户端连接已断开: ${device.name ?: deviceAddress}")
                }
            } catch (e: Exception) {
                // 忽略关闭时的错误
            }
        }
    }

    private fun notifyPermissionRequired(permissions: Array<String>) {
        callbacks.forEach { it.onPermissionRequired(permissions) }
    }

    // 存储当前控制会话的socket，用于发送扫描结果
    private var currentControlSocket: BluetoothSocket? = null

    // 存储扫描到的设备列表，用于扫描完成时一次性发送
    private val scannedDevicesList = mutableListOf<BluetoothDeviceInfo>()

    /**
     * 处理树莓派控制指令（支持JSON和传统格式）
     */
    private fun processRaspberryPiCommand(command: String, socket: BluetoothSocket) {
        try {
            handler.post {
                notifyInfo("处理树莓派控制指令: $command")
            }

            // 尝试解析JSON格式
            if (command.trim().startsWith("{")) {
                processJsonCommand(command, socket)
                return
            }

            // 传统格式处理
            val parts = command.split(":")
            if (parts.size < 2) return

            val commandType = parts[0]
            val operation = parts[1]

            when (commandType) {
                "A0" -> { // 树莓派蓝牙管理指令
                    when (operation) {
                        "01" -> { // 开始扫描
                            handler.post {
                                notifyInfo("执行指令：开始BLE扫描")
                            }
                            // 清空之前的扫描结果
                            scannedDevicesList.clear()
                            // 保存控制socket，用于发送扫描结果
                            currentControlSocket = socket
                            handler.post {
                                notifyInfo("已保存控制socket，准备设置扫描回调")
                            }
                            // 设置扫描结果回调
                            setupScanResultCallback()
                            handler.post {
                                notifyInfo("扫描回调设置完成，开始BLE扫描")
                            }
                            val scanStarted = startBleScan()
                            handler.post {
                                notifyInfo("BLE扫描启动结果: $scanStarted")
                            }
                            sendResponse(socket, "A0:01:OK")
                        }
                        "00" -> { // 停止扫描
                            handler.post {
                                notifyInfo("执行指令：停止BLE扫描")
                            }
                            stopBleScan()
                            // 清除控制socket
                            currentControlSocket = null
                            sendResponse(socket, "A0:00:OK")
                        }

                    }
                }
                "A1" -> { // 外设控制指令
                    val operation = parts[1]
                    when (operation) {
                        "CONNECT" -> { // 连接设备
                            if (parts.size >= 3) {
                                val deviceAddress = parts[2]
                                handler.post {
                                    notifyInfo("执行指令：连接BLE设备 $deviceAddress")
                                }

                                // 异步连接设备
                                Thread {
                                    try {
                                        val success = connectToBleDevice(deviceAddress)
                                        handler.post {
                                            if (success) {
                                                notifyInfo("BLE设备连接成功: $deviceAddress")
                                                sendResponse(socket, "A1:CONNECT:$deviceAddress:OK")
                                            } else {
                                                notifyError("BLE设备连接失败: $deviceAddress")
                                                sendResponse(socket, "A1:CONNECT:$deviceAddress:FAIL:连接失败")
                                            }
                                        }
                                    } catch (e: Exception) {
                                        handler.post {
                                            notifyError("BLE设备连接异常: $deviceAddress - ${e.message}")
                                            sendResponse(socket, "A1:CONNECT:$deviceAddress:FAIL:${e.message}")
                                        }
                                    }
                                }.start()

                                // 立即发送确认收到连接指令
                                sendResponse(socket, "A1:CONNECT:$deviceAddress:RECEIVED")
                            }
                        }
                        else -> { // 其他外设控制指令（原有逻辑）
                            if (parts.size >= 4) {
                                val deviceType = parts[1]
                                val deviceCommand = parts[2]
                                val deviceAddress = parts[3]

                                handler.post {
                                    notifyInfo("执行指令：控制外设 $deviceAddress (类型:$deviceType, 指令:$deviceCommand)")
                                }

                                // 发送BLE指令到外设
                                val bleCommand = byteArrayOf(deviceType.toInt(16).toByte(), deviceCommand.toInt(16).toByte())
                                val characteristicUuid = java.util.UUID.fromString("0000FFE1-0000-1000-8000-00805F9B34FB")
                                if (getBleManager().writeCharacteristic(deviceAddress, characteristicUuid, bleCommand)) {
                                    sendResponse(socket, "A1:$deviceType:$deviceCommand:$deviceAddress:OK")
                                } else {
                                    sendResponse(socket, "A1:$deviceType:$deviceCommand:$deviceAddress:FAIL")
                                }
                            }
                        }
                    }
                }
                "A2" -> { // 状态查询指令
                    when (operation) {
                        "00" -> { // 查询所有已连接设备
                            handler.post {
                                notifyInfo("执行指令：查询已连接设备")
                            }
                            val connectedDevices = getBleManager().getConnectedBleDevices()
                            val deviceList = connectedDevices.joinToString(";") { address ->
                                // 格式: 设备类型|设备名|设备地址
                                val deviceType = getDeviceTypeFromAddress(address)
                                val deviceName = getDeviceNameFromAddress(address)
                                "$deviceType|$deviceName|$address"
                            }
                            sendResponse(socket, "A2:00:$deviceList")
                        }
                    }
                }
            }
        } catch (e: Exception) {
            handler.post {
                notifyError("处理树莓派控制指令失败: ${e.message}")
            }
        }
    }

    /**
     * 设置扫描结果回调，将扫描到的设备发送给手机
     */
    private fun setupScanResultCallback() {
        handler.post {
            notifyInfo("🔧 设置BLE扫描结果回调")
        }

        // 添加一个临时回调到BLE管理器来捕获扫描结果
        val scanResultCallback = object : BluetoothCallback {
            override fun onDeviceFound(device: BluetoothDeviceInfo) {
                handler.post {
                    notifyInfo("🔍 BLE扫描发现设备: ${device.name ?: "未知"} (${device.address}) RSSI: ${device.rssi}")
                }

                // 调试模式：显示所有扫描到的设备
                println("🔍 [调试] 扫描到设备:")
                println("🔍   - 设备名称: '${device.name}'")
                println("🔍   - 设备地址: ${device.address}")
                println("🔍   - 信号强度: ${device.rssi}")
                println("🔍   - 是否有名称: ${device.name != null}")

                // 临时调试模式：发送所有有名称的设备（便于调试）
                val debugMode = true // 设置为true以显示所有设备

                val shouldSendDevice = if (debugMode) {
                    device.name != null && device.name!!.isNotEmpty()
                } else {
                    device.name != null && isTargetPeripheral(device)
                }

                if (shouldSendDevice) {
                    val deviceType = getDeviceTypeFromName(device.name!!)

                    val passedFilter = if (debugMode) "调试模式" else "通过过滤"
                    handler.post {
                        notifyInfo("✅ 设备[$passedFilter]，添加到扫描列表: ${device.name} (${device.address})")
                    }

                    // 检查是否已存在相同地址的设备，避免重复添加
                    val existingDeviceIndex = scannedDevicesList.indexOfFirst { it.address == device.address }
                    if (existingDeviceIndex == -1) {
                        // 创建新的设备信息，包含设备类型
                        val deviceWithType = BluetoothDeviceInfo(
                            name = device.name,
                            address = device.address,
                            rssi = device.rssi,
                            deviceClass = device.deviceClass,
                            bondState = device.bondState
                        )
                        scannedDevicesList.add(deviceWithType)

                        handler.post {
                            notifyInfo("📋 设备已添加到列表，当前总数: ${scannedDevicesList.size}")
                        }
                    } else {
                        // 更新现有设备的信号强度
                        scannedDevicesList[existingDeviceIndex] = scannedDevicesList[existingDeviceIndex].copy(rssi = device.rssi)
                        handler.post {
                            notifyInfo("🔄 更新设备信号强度: ${device.name} RSSI: ${device.rssi}")
                        }
                    }
                } else {
                    handler.post {
                        notifyInfo("⚠️ 设备不符合过滤条件: ${device.name ?: "未知设备"}")
                    }
                }
            }

            override fun onScanStateChanged(state: BluetoothScanState) {
                handler.post {
                    notifyInfo("🔄 BLE扫描状态变化: $state")
                }

                // 当扫描完成时，发送包含所有设备的完整消息
                if (state == BluetoothScanState.SCAN_FINISHED) {
                    currentControlSocket?.let { socket ->
                        try {
                            // 构建设备列表字符串
                            val deviceListString = scannedDevicesList.joinToString(";") { device ->
                                val deviceType = getDeviceTypeFromName(device.name ?: "Unknown")
                                "$deviceType|${device.name ?: "Unknown"}|${device.address}|${device.rssi}"
                            }

                            // 发送扫描完成消息，包含设备数量和所有设备信息
                            val response = "A0:SCAN_COMPLETE:${scannedDevicesList.size}:$deviceListString\n"
                            val outputStream = socket.outputStream
                            outputStream.write(response.toByteArray(Charsets.UTF_8))
                            outputStream.flush()

                            handler.post {
                                notifyInfo("📤 发送扫描完成通知，包含 ${scannedDevicesList.size} 个设备")
                            }

                            // 清空设备列表，为下次扫描做准备
                            scannedDevicesList.clear()
                            // 清空控制socket
                            currentControlSocket = null

                        } catch (e: Exception) {
                            handler.post {
                                notifyError("❌ 发送扫描完成通知失败: ${e.message}")
                            }
                        }
                    }
                }
            }
        }

        // 添加回调到BLE管理器
        println("🔧 [回调] 正在添加扫描回调到BLE管理器...")
        getBleManager().addCallback(scanResultCallback)
        println("🔧 [回调] 扫描回调已添加到BLE管理器")

        handler.post {
            notifyInfo("✅ BLE扫描回调已设置完成")
        }

        // 延长回调保持时间，确保扫描过程中不被移除
        handler.postDelayed({
            getBleManager().removeCallback(scanResultCallback)
            handler.post {
                notifyInfo("🔧 BLE扫描回调已移除")
            }
        }, 60000) // 60秒后移除回调，给足够时间完成扫描
    }

    /**
     * 判断是否是目标外设
     */
    private fun isTargetPeripheral(device: BluetoothDeviceInfo): Boolean {
        val name = device.name ?: return false
        val nameLower = name.lowercase()

        // 使用配置中的设备前缀进行过滤
        val isKnownPeripheral = name.startsWith("SMART-WC", ignoreCase = true) ||  // 充电器
                               name.startsWith("SMART-ML", ignoreCase = true) ||  // 主灯
                               name.startsWith("SMART-AL", ignoreCase = true) ||  // 氛围灯
                               name.startsWith("SMART-DF", ignoreCase = true) ||  // 香薰
                               name.startsWith("SMART-FN", ignoreCase = true) ||  // 风扇
                               name.startsWith("Dotix", ignoreCase = true) ||     // 点云灵动键
                               // 兼容性：也支持包含关键词的方式
                               nameLower.contains("charger") ||
                               nameLower.contains("lamp") ||
                               nameLower.contains("light") ||
                               nameLower.contains("aroma") ||
                               nameLower.contains("fan") ||
                               nameLower.contains("无线充电") ||
                               nameLower.contains("主灯") ||
                               nameLower.contains("氛围灯") ||
                               nameLower.contains("香氛") ||
                               nameLower.contains("风扇") ||
                               nameLower.contains("点云") ||
                               nameLower.contains("灵动键") ||
                               nameLower.contains("按键")

        // 测试模式：也包含测试设备
        val isTestDevice = name.contains("ble") ||
                          name.contains("test") ||
                          name.contains("demo") ||
                          name.contains("device")

        // 信号强度过滤：只接受信号强度较好的设备（放宽到-90以便调试）
        val hasGoodSignal = device.rssi >= -90

        val result = (isKnownPeripheral || isTestDevice) && hasGoodSignal

        // 增强调试信息
        println("🔍 设备过滤检查: ${device.name}")
        println("🔍   - 设备名称(小写): $name")
        println("🔍   - 已知外设: $isKnownPeripheral")
        println("🔍   - 测试设备: $isTestDevice")
        println("🔍   - 信号强度: ${device.rssi} (要求>=-90)")
        println("🔍   - 信号合格: $hasGoodSignal")
        println("🔍   - 最终结果: $result")

        if (!result) {
            Log.d(TAG, "设备过滤: ${device.name} - 已知外设:$isKnownPeripheral, 测试设备:$isTestDevice, 信号强度:${device.rssi}, 结果:$result")
        } else {
            println("✅ 设备通过过滤: ${device.name}")
        }

        return result
    }

    /**
     * 根据设备名称推断设备类型
     */
    private fun getDeviceTypeFromName(deviceName: String): String {
        val name = deviceName
        val nameLower = deviceName.lowercase()

        return when {
            // 使用配置前缀进行精确匹配
            name.startsWith("Dotix", ignoreCase = true) -> "01" // 点云灵动键
            name.startsWith("SMART-WC", ignoreCase = true) -> "02" // 充电器
            name.startsWith("SMART-ML", ignoreCase = true) -> "03" // 主灯
            name.startsWith("SMART-AL", ignoreCase = true) -> "04" // 氛围灯
            name.startsWith("SMART-DF", ignoreCase = true) -> "05" // 香薰
            name.startsWith("SMART-FN", ignoreCase = true) -> "06" // 风扇
            // 兼容性：支持包含关键词的方式
            nameLower.contains("点云") || nameLower.contains("灵动键") || nameLower.contains("按键") -> "01"
            nameLower.contains("charger") || nameLower.contains("无线充电") -> "02"
            nameLower.contains("lamp") || nameLower.contains("主灯") -> "03"
            nameLower.contains("light") || nameLower.contains("氛围灯") -> "04"
            nameLower.contains("aroma") || nameLower.contains("香氛") -> "05"
            nameLower.contains("fan") || nameLower.contains("风扇") -> "06"
            else -> "01" // 默认类型（点云灵动键）
        }
    }

    /**
     * 发送响应到客户端
     */
    private fun sendResponse(socket: BluetoothSocket, response: String) {
        try {
            val outputStream = socket.outputStream
            // 确保响应以换行符结尾，作为帧结束符
            val responseWithNewline = if (response.endsWith("\n")) response else "$response\n"
            outputStream.write(responseWithNewline.toByteArray(Charsets.UTF_8))
            outputStream.flush()
            handler.post {
                notifyInfo("📤 发送响应: $responseWithNewline")
            }
        } catch (e: Exception) {
            handler.post {
                notifyError("❌ 发送响应失败: ${e.message}")
            }
        }
    }

    /**
     * 根据设备地址获取设备类型（简化实现）
     */
    private fun getDeviceTypeFromAddress(address: String): String {
        // 这里可以根据设备地址或名称来判断设备类型
        // 简化实现，返回默认类型
        return "02" // 默认为充电器类型
    }

    /**
     * 根据设备地址获取设备名称（简化实现）
     */
    private fun getDeviceNameFromAddress(address: String): String {
        // 这里可以从已连接设备列表中获取真实设备名称
        // 简化实现，返回地址的简化版本
        return "设备-${address.takeLast(4)}"
    }

    /**
     * 处理JSON格式的命令
     */
    private fun processJsonCommand(command: String, socket: BluetoothSocket) {
        try {
            val json = JSONObject(command)
            val cmd = json.getString("cmd")

            handler.post {
                notifyInfo("处理JSON指令: $cmd")
            }

            when (cmd) {
                "scan_start" -> {
                    val timeout = json.optInt("timeout", 10)
                    handler.post {
                        notifyInfo("执行JSON指令：开始BLE扫描 (超时: ${timeout}秒)")
                    }

                    // 保存控制socket
                    currentControlSocket = socket
                    setupScanResultCallback()

                    val scanStarted = startBleScan()

                    // 发送JSON响应
                    val response = JSONObject().apply {
                        put("type", "scan_result")
                        put("status", if (scanStarted) "ok" else "fail")
                    }
                    sendJsonResponse(socket, response)

                    // 设置扫描超时
                    handler.postDelayed({
                        stopBleScan()
                        currentControlSocket = null
                    }, timeout * 1000L)
                }

                "scan_stop" -> {
                    handler.post {
                        notifyInfo("执行JSON指令：停止BLE扫描")
                    }
                    stopBleScan()
                    currentControlSocket = null

                    val response = JSONObject().apply {
                        put("type", "scan_result")
                        put("status", "stopped")
                    }
                    sendJsonResponse(socket, response)
                }

                "connect" -> {
                    val mac = json.getString("mac")
                    val profile = json.optString("profile", "ble")

                    handler.post {
                        notifyInfo("执行JSON指令：连接设备 $mac ($profile)")
                    }

                    val connected = when (profile) {
                        "ble" -> connectToBleDevice(mac)
                        else -> false
                    }

                    val response = JSONObject().apply {
                        put("type", "connect_result")
                        put("status", if (connected) "ok" else "fail")
                        put("mac", mac)
                    }
                    sendJsonResponse(socket, response)
                }

                "disconnect" -> {
                    val mac = json.getString("mac")

                    handler.post {
                        notifyInfo("执行JSON指令：断开设备 $mac")
                    }

                    // 断开BLE设备
                    getBleManager().disconnectBleDevice(mac)

                    val response = JSONObject().apply {
                        put("type", "disconnect_result")
                        put("status", "ok")
                        put("mac", mac)
                    }
                    sendJsonResponse(socket, response)
                }

                else -> {
                    val response = JSONObject().apply {
                        put("type", "error")
                        put("reason", "Unknown command: $cmd")
                    }
                    sendJsonResponse(socket, response)
                }
            }

        } catch (e: Exception) {
            handler.post {
                notifyError("处理JSON指令失败: ${e.message}")
            }

            val response = JSONObject().apply {
                put("type", "error")
                put("reason", "JSON parsing error: ${e.message}")
            }
            sendJsonResponse(socket, response)
        }
    }

    /**
     * 发送JSON响应
     */
    private fun sendJsonResponse(socket: BluetoothSocket, response: JSONObject) {
        try {
            val responseString = response.toString() + "\n"
            val outputStream = socket.outputStream
            outputStream.write(responseString.toByteArray(Charsets.UTF_8))
            outputStream.flush()

            handler.post {
                notifyInfo("📤 发送JSON响应: $responseString")
            }
        } catch (e: Exception) {
            handler.post {
                notifyError("❌ 发送JSON响应失败: ${e.message}")
            }
        }
    }

    // ==================== BLE功能 ====================

    /**
     * 获取BLE管理器实例，延迟初始化
     */
    private fun getBleManager(): BleManager {
        if (bleManager == null) {
            bleManager = BleManager.getInstance(context)
            setupBleManagerCallbacks()
        }
        return bleManager!!
    }

    /**
     * 设置BLE管理器回调
     */
    private fun setupBleManagerCallbacks() {
        bleManager!!.addCallback(object : BluetoothCallback {
            override fun onDeviceFound(device: BluetoothDeviceInfo) {
                // 转发BLE设备发现事件
                notifyDeviceFound(device)
            }

            override fun onError(error: String) {
                // 转发BLE错误事件
                notifyError(error)
            }

            override fun onSuccess(message: String) {
                // 转发BLE成功事件
                notifySuccess(message)
            }

            override fun onInfo(message: String) {
                // 转发BLE信息事件
                notifyInfo(message)
            }

            override fun onCharacteristicNotification(deviceAddress: String, characteristicUuid: String, data: ByteArray) {
                // 转发BLE特征值通知事件
                callbacks.forEach { it.onCharacteristicNotification(deviceAddress, characteristicUuid, data) }
            }

            override fun onBleConnectionStateChanged(deviceAddress: String, state: Int) {
                // 转发BLE连接状态变化事件
                callbacks.forEach { it.onBleConnectionStateChanged(deviceAddress, state) }

                // 如果是连接成功，自动订阅0xFFE4特征值
                if (state == 2) { // BluetoothProfile.STATE_CONNECTED
                    Log.d(TAG, "BLE设备连接成功，自动订阅0xFFE4特征值: $deviceAddress")
                    // 延迟一点时间确保服务发现完成
                    handler.postDelayed({
                        subscribeToFFE4Notification(deviceAddress)
                    }, 1000)
                }
            }
        })
    }

    /**
     * 开始BLE扫描
     */
    fun startBleScan(): Boolean {
        println("🔍 [扫描] 开始BLE扫描...")
        val result = getBleManager().startBleScan()
        println("🔍 [扫描] BLE扫描启动结果: $result")
        return result
    }

    /**
     * 停止BLE扫描
     */
    fun stopBleScan() {
        getBleManager().stopBleScan()
    }

    /**
     * 连接到BLE设备
     */
    fun connectToBleDevice(deviceAddress: String): Boolean {
        return getBleManager().connectToBleDeviceWithPreparation(deviceAddress)
    }

    /**
     * 订阅0xFFE4特征值的Notify通知
     */
    fun subscribeToFFE4Notification(deviceAddress: String): Boolean {
        val ffe4Uuid = java.util.UUID.fromString("0000FFE4-0000-1000-8000-00805F9B34FB")
        return getBleManager().subscribeToCharacteristic(deviceAddress, ffe4Uuid)
    }

    /**
     * 订阅指定特征值的Notify通知
     */
    fun subscribeToCharacteristic(deviceAddress: String, characteristicUuid: java.util.UUID): Boolean {
        return getBleManager().subscribeToCharacteristic(deviceAddress, characteristicUuid)
    }

    /**
     * 断开BLE设备连接
     */
    fun disconnectBleDevice(deviceAddress: String) {
        getBleManager().disconnectBleDevice(deviceAddress)
    }

    /**
     * 获取已连接的BLE设备列表
     */
    fun getConnectedBleDevices(): List<String> {
        return getBleManager().getConnectedBleDevices()
    }

    /**
     * 自动连接到指定的BLE设备并订阅0xFFE4特征值
     * 这个方法通常在统一蓝牙服务器中使用
     */
    fun autoConnectAndSubscribeBleDevice(deviceAddress: String): Boolean {
        Log.d(TAG, "自动连接并订阅BLE设备: $deviceAddress")

        // 先尝试连接
        val connected = connectToBleDevice(deviceAddress)
        if (connected) {
            // 连接成功后，订阅会在连接状态回调中自动处理
            Log.d(TAG, "BLE设备连接请求已发送，等待连接完成后自动订阅")
            return true
        } else {
            Log.e(TAG, "BLE设备连接失败: $deviceAddress")
            return false
        }
    }

    /**
     * 批量连接多个BLE设备
     */
    fun autoConnectMultipleBleDevices(deviceAddresses: List<String>) {
        Log.d(TAG, "批量连接BLE设备: ${deviceAddresses.size}个设备")
        deviceAddresses.forEach { address ->
            autoConnectAndSubscribeBleDevice(address)
        }
    }

    /**
     * 诊断BLE设备的详细信息
     * 用于排查BLE设备兼容性问题
     */
    fun diagnoseBleDevice(deviceAddress: String): Boolean {
        return getBleManager().diagnoseBleDevice(deviceAddress)
    }

    /**
     * 诊断BLE连接问题
     * 用于排查连接失败的原因
     */
    fun diagnoseBleConnectionIssues(deviceAddress: String) {
        getBleManager().diagnoseConnectionIssues(deviceAddress)
    }

    // ==================== 外设控制功能 ====================

    /**
     * 向BLE设备的特征值写入数据
     */
    fun writeCharacteristic(deviceAddress: String, characteristicUuid: java.util.UUID, data: ByteArray): Boolean {
        return getBleManager().writeCharacteristic(deviceAddress, characteristicUuid, data)
    }

    /**
     * 发送外设控制指令
     */
    fun sendPeripheralCommand(deviceAddress: String, deviceType: Byte, command: Byte): Boolean {
        return getBleManager().sendPeripheralCommand(deviceAddress, deviceType, command)
    }

    /**
     * 控制无线充电器
     */
    fun controlWirelessCharger(deviceAddress: String, enable: Boolean): Boolean {
        return getBleManager().controlWirelessCharger(deviceAddress, enable)
    }

    /**
     * 控制主灯
     */
    fun controlMainLamp(deviceAddress: String, enable: Boolean): Boolean {
        return getBleManager().controlMainLamp(deviceAddress, enable)
    }

    /**
     * 控制氛围灯
     */
    fun controlAmbientLight(deviceAddress: String, enable: Boolean): Boolean {
        return getBleManager().controlAmbientLight(deviceAddress, enable)
    }

    /**
     * 控制香氛机
     */
    fun controlAromaDiffuser(deviceAddress: String, enable: Boolean): Boolean {
        return getBleManager().controlAromaDiffuser(deviceAddress, enable)
    }

    /**
     * 控制风扇
     */
    fun controlFan(deviceAddress: String, enable: Boolean): Boolean {
        return getBleManager().controlFan(deviceAddress, enable)
    }

    /**
     * 查询外设状态
     */
    fun queryPeripheralStatus(deviceAddress: String, deviceType: Byte): Boolean {
        return getBleManager().queryPeripheralStatus(deviceAddress, deviceType)
    }
}
