package com.zerosense.bluetooth

import android.bluetooth.*
import android.bluetooth.le.*
import android.content.Context
import android.os.Handler
import android.os.Looper
import android.util.Log
import java.util.*
import java.util.concurrent.ConcurrentHashMap

/**
 * BLE（蓝牙低功耗）管理器
 * 负责BLE设备的连接、特征值订阅和数据接收
 */
class BleManager private constructor(private val context: Context) {
    
    companion object {
        private const val TAG = "BleManager"
        
        @Volatile
        private var INSTANCE: BleManager? = null
        
        fun getInstance(context: Context): BleManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: BleManager(context.applicationContext).also { INSTANCE = it }
            }
        }
        
        // 通用BLE外设服务和特征值UUID
        private val SERVICE_FFE0_UUID = UUID.fromString("0000FFE0-0000-1000-8000-00805F9B34FB") // 主服务
        private val CHARACTERISTIC_FFE1_UUID = UUID.fromString("0000FFE1-0000-1000-8000-00805F9B34FB") // 控制特征值
        private val CHARACTERISTIC_FFE2_UUID = UUID.fromString("0000FFE2-0000-1000-8000-00805F9B34FB") // 状态特征值
        private val CHARACTERISTIC_FFE4_UUID = UUID.fromString("0000FFE4-0000-1000-8000-00805F9B34FB") // 按键通知
        private val CHARACTERISTIC_FFE5_UUID = UUID.fromString("0000FFE5-0000-1000-8000-00805F9B34FB") // 设备基本信息
        
        // 客户端特征配置描述符UUID（用于启用通知）
        private val CLIENT_CHARACTERISTIC_CONFIG_UUID = UUID.fromString("00002902-0000-1000-8000-00805F9B34FB")
        
        // BLE扫描超时时间
        private const val BLE_SCAN_TIMEOUT = 10000L

        // 连接重试相关常量
        private const val MAX_CONNECTION_RETRIES = 3  // 最大重试次数
        private const val RETRY_DELAY_MS = 2000L      // 重试延迟（毫秒）
        private const val CONNECTION_TIMEOUT_MS = 15000L  // 连接超时（毫秒）

        // 连接保活相关常量
        private const val KEEPALIVE_INTERVAL_MS = 30000L  // 保活间隔30秒
        private const val CONNECTION_CHECK_INTERVAL_MS = 10000L  // 连接检查间隔10秒
        private const val MAX_MISSED_KEEPALIVE = 3  // 最大错过保活次数
    }
    
    private val bluetoothAdapter: BluetoothAdapter? = BluetoothAdapter.getDefaultAdapter()
    private val bluetoothLeScanner: BluetoothLeScanner? = bluetoothAdapter?.bluetoothLeScanner
    private val bluetoothManager: android.bluetooth.BluetoothManager? = context.getSystemService(Context.BLUETOOTH_SERVICE) as? android.bluetooth.BluetoothManager
    private val handler = Handler(Looper.getMainLooper())
    
    // GATT连接管理
    private val gattConnections = ConcurrentHashMap<String, BluetoothGatt>()
    private val connectionCallbacks = ConcurrentHashMap<String, BluetoothGattCallback>()
    private val connectionRetryCount = ConcurrentHashMap<String, Int>()  // 连接重试计数器
    private val connectionTimeouts = ConcurrentHashMap<String, Runnable>()  // 连接超时任务

    // 连接保活管理
    private val keepaliveTimers = ConcurrentHashMap<String, Runnable>()  // 保活定时器
    private val connectionHealthCheck = ConcurrentHashMap<String, Runnable>()  // 连接健康检查
    private val lastKeepaliveTime = ConcurrentHashMap<String, Long>()  // 最后保活时间
    private val missedKeepaliveCount = ConcurrentHashMap<String, Int>()  // 错过保活次数
    
    // 回调管理
    private val callbacks = mutableSetOf<BluetoothCallback>()
    
    // 扫描相关
    private var isScanning = false
    private var scanCallback: ScanCallback? = null
    
    /**
     * 添加回调监听器
     */
    fun addCallback(callback: BluetoothCallback) {
        callbacks.add(callback)
    }
    
    /**
     * 移除回调监听器
     */
    fun removeCallback(callback: BluetoothCallback) {
        callbacks.remove(callback)
    }

    /**
     * 获取当前回调数量（用于调试）
     */
    fun getCallbackCount(): Int {
        return callbacks.size
    }
    
    /**
     * 开始BLE扫描
     */
    fun startBleScan(): Boolean {
        if (bluetoothLeScanner == null) {
            notifyError("BLE扫描器不可用")
            return false
        }
        
        if (isScanning) {
            notifyInfo("BLE扫描已在进行中")
            return true
        }
        
        scanCallback = object : ScanCallback() {
            override fun onScanResult(callbackType: Int, result: ScanResult) {
                val device = result.device
                val rssi = result.rssi
                
                Log.d(TAG, "发现BLE设备: ${device.name ?: "未知"} (${device.address}) RSSI: $rssi")
                
                val deviceInfo = BluetoothDeviceInfo.fromBluetoothDevice(device, rssi)
                notifyDeviceFound(deviceInfo)
            }
            
            override fun onScanFailed(errorCode: Int) {
                notifyError("BLE扫描失败，错误代码: $errorCode")
                isScanning = false
            }
        }
        
        try {
            bluetoothLeScanner.startScan(scanCallback)
            isScanning = true
            notifyInfo("开始BLE扫描")
            
            // 设置扫描超时
            handler.postDelayed({
                stopBleScan()
            }, BLE_SCAN_TIMEOUT)
            
            return true
        } catch (e: Exception) {
            notifyError("启动BLE扫描失败: ${e.message}")
            return false
        }
    }
    
    /**
     * 停止BLE扫描
     */
    fun stopBleScan() {
        if (!isScanning) return
        
        try {
            scanCallback?.let { bluetoothLeScanner?.stopScan(it) }
            isScanning = false
            notifyInfo("BLE扫描已停止")
        } catch (e: Exception) {
            notifyError("停止BLE扫描失败: ${e.message}")
        }
    }
    
    /**
     * 连接到BLE设备
     */
    fun connectToBleDevice(deviceAddress: String): Boolean {
        Log.d(TAG, "🔗 开始连接BLE设备: $deviceAddress")

        // 验证蓝牙适配器
        if (bluetoothAdapter == null) {
            notifyError("蓝牙适配器不可用")
            return false
        }

        if (!bluetoothAdapter.isEnabled) {
            notifyError("蓝牙未启用")
            return false
        }

        // 验证设备地址格式
        if (!BluetoothAdapter.checkBluetoothAddress(deviceAddress)) {
            notifyError("无效的设备地址格式: $deviceAddress")
            return false
        }

        val device = bluetoothAdapter.getRemoteDevice(deviceAddress)
        if (device == null) {
            notifyError("设备不存在: $deviceAddress")
            return false
        }

        // 如果已经连接，检查连接状态
        val existingGatt = gattConnections[deviceAddress]
        if (existingGatt != null) {
            // 检查连接是否真的有效
            try {
                val connectionState = bluetoothManager?.getConnectionState(device, BluetoothProfile.GATT) ?: BluetoothProfile.STATE_DISCONNECTED
                if (connectionState == BluetoothProfile.STATE_CONNECTED) {
                    Log.d(TAG, "设备已连接: $deviceAddress")
                    notifyInfo("设备已连接: $deviceAddress")
                    return true
                } else {
                    Log.d(TAG, "清理无效的连接记录: $deviceAddress (状态: $connectionState)")
                    // 清理无效连接
                    gattConnections.remove(deviceAddress)
                    connectionCallbacks.remove(deviceAddress)
                    existingGatt.close()
                }
            } catch (e: Exception) {
                Log.e(TAG, "检查连接状态失败: ${e.message}")
                // 清理可能损坏的连接
                gattConnections.remove(deviceAddress)
                connectionCallbacks.remove(deviceAddress)
                existingGatt.close()
            }
        }

        Log.d(TAG, "准备建立新的GATT连接: $deviceAddress")
        
        val gattCallback = object : BluetoothGattCallback() {
            override fun onConnectionStateChange(gatt: BluetoothGatt, status: Int, newState: Int) {
                Log.d(TAG, "BLE连接状态变化: $deviceAddress, status=$status, newState=$newState")

                // 🔑 关键修复：首先检查status状态
                if (status != BluetoothGatt.GATT_SUCCESS) {
                    Log.e(TAG, "BLE连接失败: $deviceAddress, GATT错误码: $status")

                    val errorMessage = when (status) {
                        133 -> "GATT错误133: 连接失败，可能是设备不可达或信号问题"
                        8 -> "GATT错误8: 连接超时"
                        19 -> "GATT错误19: 设备未找到或已断开"
                        22 -> "GATT错误22: 连接被拒绝"
                        else -> "GATT错误$status: 连接失败"
                    }

                    handler.post {
                        notifyError(errorMessage)
                        notifyBleConnectionStateChanged(deviceAddress, BluetoothProfile.STATE_DISCONNECTED)
                    }

                    // 清理失败的连接
                    gattConnections.remove(deviceAddress)
                    connectionCallbacks.remove(deviceAddress)
                    clearConnectionTimeout(deviceAddress)
                    gatt.close()

                    // 🔄 实现重连机制（针对GATT错误133和错误8）
                    if (status == 133 || status == 8) {
                        val retryCount = connectionRetryCount.getOrDefault(deviceAddress, 0)
                        if (retryCount < MAX_CONNECTION_RETRIES) {
                            connectionRetryCount[deviceAddress] = retryCount + 1
                            val errorType = if (status == 133) "连接失败" else "连接超时"
                            Log.d(TAG, "GATT错误$status ($errorType)，准备重连 (第${retryCount + 1}次): $deviceAddress")

                            // 对于连接超时，使用更长的重试延迟
                            val retryDelay = if (status == 8) RETRY_DELAY_MS * 2 else RETRY_DELAY_MS

                            handler.postDelayed({
                                Log.d(TAG, "开始重连: $deviceAddress")
                                connectToBleDevice(deviceAddress)
                            }, retryDelay)
                        } else {
                            Log.e(TAG, "重连次数已达上限，放弃连接: $deviceAddress")
                            connectionRetryCount.remove(deviceAddress)
                            handler.post {
                                val errorType = if (status == 133) "连接失败" else "连接超时"
                                notifyError("$errorType：已重试${MAX_CONNECTION_RETRIES}次，请检查设备状态和距离")
                            }
                        }
                    }
                    return
                }

                // status正常，处理连接状态
                when (newState) {
                    BluetoothProfile.STATE_CONNECTED -> {
                        Log.d(TAG, "BLE设备连接成功: $deviceAddress")
                        // 清除重试计数和超时任务
                        connectionRetryCount.remove(deviceAddress)
                        clearConnectionTimeout(deviceAddress)

                        handler.post {
                            notifySuccess("BLE设备连接成功: $deviceAddress")
                            notifyBleConnectionStateChanged(deviceAddress, newState)
                        }

                        // 🔧 根据绑定状态智能处理服务发现
                        handleServiceDiscoveryAfterConnection(gatt, deviceAddress)

                        // 🔄 启动连接保活机制
                        startConnectionKeepalive(deviceAddress)
                    }
                    BluetoothProfile.STATE_DISCONNECTED -> {
                        Log.d(TAG, "BLE设备已断开: $deviceAddress")
                        handler.post {
                            notifyInfo("BLE设备已断开: $deviceAddress")
                            notifyBleConnectionStateChanged(deviceAddress, newState)
                        }
                        // 清理连接
                        gattConnections.remove(deviceAddress)
                        connectionCallbacks.remove(deviceAddress)
                        connectionRetryCount.remove(deviceAddress)
                        clearConnectionTimeout(deviceAddress)

                        // 🔄 停止连接保活机制
                        stopConnectionKeepalive(deviceAddress)

                        gatt.close()
                    }
                    BluetoothProfile.STATE_CONNECTING -> {
                        Log.d(TAG, "BLE设备正在连接: $deviceAddress")
                        handler.post {
                            notifyInfo("正在连接BLE设备: $deviceAddress")
                            notifyBleConnectionStateChanged(deviceAddress, newState)
                        }
                    }
                    BluetoothProfile.STATE_DISCONNECTING -> {
                        Log.d(TAG, "BLE设备正在断开: $deviceAddress")
                        handler.post {
                            notifyInfo("正在断开BLE设备: $deviceAddress")
                            notifyBleConnectionStateChanged(deviceAddress, newState)
                        }
                    }
                }
            }
            
            override fun onServicesDiscovered(gatt: BluetoothGatt, status: Int) {
                Log.d(TAG, "服务发现回调: $deviceAddress, status=$status")

                if (status == BluetoothGatt.GATT_SUCCESS) {
                    val serviceCount = gatt.services.size
                    Log.d(TAG, "✅ 服务发现成功: $deviceAddress，发现${serviceCount}个服务")

                    // 详细打印所有发现的服务和特征值
                    gatt.services.forEach { service ->
                        Log.d(TAG, "📋 服务: ${service.uuid}")
                        service.characteristics.forEach { characteristic ->
                            Log.d(TAG, "  └─ 特征值: ${characteristic.uuid} (属性: ${characteristic.properties})")
                        }
                    }

                    // 检查是否找到目标服务
                    val targetServiceFound = gatt.services.any { service ->
                        service.uuid.toString().equals("0000FFE0-0000-1000-8000-00805F9B34FB", ignoreCase = true)
                    }

                    if (targetServiceFound) {
                        Log.d(TAG, "🎯 找到目标服务 0xFFE0")
                        handler.post {
                            notifySuccess("服务发现成功，找到目标服务 (共${serviceCount}个服务)")
                        }
                    } else {
                        Log.w(TAG, "⚠️ 未找到目标服务 0xFFE0，但服务发现成功")
                        handler.post {
                            notifyWarning("服务发现成功但未找到目标服务 0xFFE0")
                        }
                    }

                    // 无论是否找到目标服务，都尝试初始化（可能设备使用不同UUID）
                    handler.post {
                        initializeDotixDevice(deviceAddress)
                    }
                } else {
                    val errorMessage = when (status) {
                        129 -> "GATT错误129: 服务发现失败，内部错误 - 可能是连接不稳定"
                        133 -> "GATT错误133: 服务发现失败，连接问题 - 设备可能断开连接"
                        8 -> "GATT错误8: 服务发现超时"
                        else -> "GATT错误$status: 服务发现失败"
                    }

                    Log.e(TAG, "❌ $errorMessage: $deviceAddress")
                    handler.post {
                        notifyError("$errorMessage\n建议：重新连接设备或检查设备状态")
                    }

                    // 服务发现失败，延迟后断开连接（给用户时间看到错误信息）
                    handler.postDelayed({
                        Log.d(TAG, "服务发现失败，断开连接: $deviceAddress")
                        gatt.disconnect()
                    }, 2000)
                }
            }
            
            override fun onCharacteristicChanged(gatt: BluetoothGatt, characteristic: BluetoothGattCharacteristic) {
                val uuid = characteristic.uuid.toString().uppercase()
                val data = characteristic.value

                Log.d(TAG, "🔔 收到特征值通知!")
                Log.d(TAG, "  设备地址: $deviceAddress")
                Log.d(TAG, "  特征值UUID: $uuid")
                Log.d(TAG, "  数据长度: ${data?.size ?: 0}")
                if (data != null && data.isNotEmpty()) {
                    val dataHex = data.joinToString(" ") { "%02X".format(it) }
                    Log.d(TAG, "  数据内容: $dataHex")

                    // 根据特征值类型解析数据
                    when {
                        uuid.contains("FFE4") -> {
                            // 按键通知数据解析
                            val buttonInfo = parseButtonData(data)
                            Log.d(TAG, "  按键解析: $buttonInfo")
                        }
                        uuid.contains("FFE2") -> {
                            // 状态反馈数据解析
                            val statusInfo = parseStatusData(data)
                            Log.d(TAG, "  状态解析: $statusInfo")
                        }
                    }
                }

                handler.post {
                    data?.let {
                        Log.d(TAG, "转发特征值通知到回调")
                        notifyCharacteristicNotification(deviceAddress, uuid, it)
                    }
                }
            }

            override fun onCharacteristicWrite(gatt: BluetoothGatt, characteristic: BluetoothGattCharacteristic, status: Int) {
                val characteristicUuid = characteristic.uuid.toString().uppercase()
                Log.d(TAG, "📤 特征值写入完成")
                Log.d(TAG, "  设备地址: $deviceAddress")
                Log.d(TAG, "  特征值UUID: $characteristicUuid")
                Log.d(TAG, "  写入状态: $status")

                if (status == BluetoothGatt.GATT_SUCCESS) {
                    Log.d(TAG, "✅ 特征值写入成功!")
                    handler.post {
                        notifySuccess("数据写入成功: $characteristicUuid")
                    }
                } else {
                    Log.e(TAG, "❌ 特征值写入失败，状态码: $status")
                    handler.post {
                        notifyError("数据写入失败，状态码: $status")
                    }
                }
            }

            override fun onDescriptorWrite(gatt: BluetoothGatt, descriptor: BluetoothGattDescriptor, status: Int) {
                val characteristicUuid = descriptor.characteristic.uuid.toString().uppercase()
                Log.d(TAG, "📝 描述符写入完成")
                Log.d(TAG, "  设备地址: $deviceAddress")
                Log.d(TAG, "  特征值UUID: $characteristicUuid")
                Log.d(TAG, "  描述符UUID: ${descriptor.uuid}")
                Log.d(TAG, "  写入状态: $status")

                if (status == BluetoothGatt.GATT_SUCCESS) {
                    Log.d(TAG, "✅ 特征值通知订阅成功!")
                    handler.post {
                        notifySuccess("特征值通知订阅成功: $characteristicUuid")
                    }
                } else {
                    Log.e(TAG, "❌ 特征值通知订阅失败，状态码: $status")
                    handler.post {
                        notifyError("特征值通知订阅失败，状态码: $status")
                    }
                }
            }
        }
        
        try {
            // 🔗 建立GATT连接
            Log.d(TAG, "调用connectGatt: $deviceAddress")
            val gatt = device.connectGatt(context, false, gattCallback)

            if (gatt == null) {
                Log.e(TAG, "connectGatt返回null: $deviceAddress")
                notifyError("无法创建GATT连接")
                return false
            }

            // 保存连接信息
            gattConnections[deviceAddress] = gatt
            connectionCallbacks[deviceAddress] = gattCallback

            // 设置连接超时
            val timeoutTask = Runnable {
                Log.w(TAG, "⏰ BLE连接超时: $deviceAddress")
                val timeoutGatt = gattConnections[deviceAddress]
                if (timeoutGatt != null) {
                    timeoutGatt.disconnect()
                    handler.post {
                        notifyError("连接超时: $deviceAddress")
                    }
                }
            }
            connectionTimeouts[deviceAddress] = timeoutTask
            handler.postDelayed(timeoutTask, CONNECTION_TIMEOUT_MS)

            Log.d(TAG, "GATT连接已创建，等待连接结果: $deviceAddress (超时: ${CONNECTION_TIMEOUT_MS/1000}秒)")
            notifyInfo("正在连接BLE设备: $deviceAddress")
            return true

        } catch (e: SecurityException) {
            Log.e(TAG, "连接BLE设备失败，权限不足: ${e.message}")
            notifyError("连接失败：权限不足，请检查蓝牙权限")
            return false
        } catch (e: Exception) {
            Log.e(TAG, "连接BLE设备失败: ${e.message}")
            notifyError("连接BLE设备失败: ${e.message}")
            return false
        }
    }
    
    /**
     * 订阅特征值通知
     */
    fun subscribeToCharacteristic(deviceAddress: String, characteristicUuid: UUID): Boolean {
        Log.d(TAG, "📱 开始订阅特征值: $deviceAddress, UUID: $characteristicUuid")

        val gatt = gattConnections[deviceAddress]
        if (gatt == null) {
            val error = "❌ 设备未连接: $deviceAddress"
            Log.e(TAG, error)
            notifyError(error)
            return false
        }

        // 检查连接状态
        try {
            val device = bluetoothAdapter?.getRemoteDevice(deviceAddress)
            val connectionState = bluetoothManager?.getConnectionState(device!!, BluetoothProfile.GATT) ?: BluetoothProfile.STATE_DISCONNECTED
            if (connectionState != BluetoothProfile.STATE_CONNECTED) {
                val error = "❌ 设备连接状态异常: $deviceAddress (状态: $connectionState)"
                Log.e(TAG, error)
                notifyError(error)
                return false
            }
        } catch (e: Exception) {
            Log.e(TAG, "检查连接状态失败: ${e.message}")
        }

        // 检查服务是否已发现
        if (gatt.services.isEmpty()) {
            val error = "❌ 服务尚未发现，请等待服务发现完成"
            Log.e(TAG, error)
            notifyError(error)
            return false
        }

        Log.d(TAG, "✅ GATT连接有效，开始查找特征值")

        // 打印所有可用的服务和特征值
        Log.d(TAG, "📋 可用的服务和特征值 (共${gatt.services.size}个服务):")
        for (service in gatt.services) {
            Log.d(TAG, "  📁 服务: ${service.uuid}")
            for (characteristic in service.characteristics) {
                val properties = characteristic.properties
                val propertiesStr = mutableListOf<String>()
                if (properties and BluetoothGattCharacteristic.PROPERTY_READ != 0) propertiesStr.add("READ")
                if (properties and BluetoothGattCharacteristic.PROPERTY_WRITE != 0) propertiesStr.add("WRITE")
                if (properties and BluetoothGattCharacteristic.PROPERTY_NOTIFY != 0) propertiesStr.add("NOTIFY")
                if (properties and BluetoothGattCharacteristic.PROPERTY_INDICATE != 0) propertiesStr.add("INDICATE")

                Log.d(TAG, "    📄 特征值: ${characteristic.uuid}, 属性: [${propertiesStr.joinToString(", ")}]")

                // 打印描述符信息
                characteristic.descriptors.forEach { descriptor ->
                    Log.d(TAG, "      🏷️ 描述符: ${descriptor.uuid}")
                }
            }
        }

        // 查找目标特征值
        var targetCharacteristic: BluetoothGattCharacteristic? = null
        var targetService: BluetoothGattService? = null

        for (service in gatt.services) {
            for (characteristic in service.characteristics) {
                if (characteristic.uuid == characteristicUuid) {
                    targetCharacteristic = characteristic
                    targetService = service
                    Log.d(TAG, "🎯 找到目标特征值: ${characteristic.uuid} (在服务 ${service.uuid} 中)")
                    break
                }
            }
            if (targetCharacteristic != null) break
        }

        if (targetCharacteristic == null) {
            val error = "❌ 未找到特征值: $characteristicUuid"
            Log.e(TAG, error)
            Log.e(TAG, "💡 提示：请确认设备是否支持此特征值，或检查UUID是否正确")
            notifyError("未找到特征值 $characteristicUuid，请检查设备兼容性")
            return false
        }
        
        // 检查特征值是否支持通知
        val properties = targetCharacteristic.properties
        Log.d(TAG, "🔍 检查特征值属性: $properties")

        val supportsNotify = (properties and BluetoothGattCharacteristic.PROPERTY_NOTIFY) != 0
        val supportsIndicate = (properties and BluetoothGattCharacteristic.PROPERTY_INDICATE) != 0

        if (!supportsNotify && !supportsIndicate) {
            val error = "❌ 特征值不支持通知或指示功能 (属性: $properties)"
            Log.e(TAG, error)
            notifyError("特征值不支持通知功能，无法订阅")
            return false
        }

        Log.d(TAG, "✅ 特征值支持${if (supportsNotify) "通知" else ""}${if (supportsIndicate) "指示" else ""}")

        // 启用本地通知
        Log.d(TAG, "🔔 启用本地通知...")
        val notificationSuccess = gatt.setCharacteristicNotification(targetCharacteristic, true)
        if (!notificationSuccess) {
            val error = "❌ 启用本地通知失败"
            Log.e(TAG, error)
            notifyError("启用本地通知失败")
            return false
        }
        Log.d(TAG, "✅ 本地通知启用成功")

        // 查找并写入客户端特征配置描述符
        Log.d(TAG, "🔍 查找客户端特征配置描述符...")
        val descriptor = targetCharacteristic.getDescriptor(CLIENT_CHARACTERISTIC_CONFIG_UUID)

        if (descriptor == null) {
            val error = "❌ 未找到客户端特征配置描述符 (UUID: $CLIENT_CHARACTERISTIC_CONFIG_UUID)"
            Log.e(TAG, error)
            Log.e(TAG, "💡 可用的描述符:")
            targetCharacteristic.descriptors.forEach { desc ->
                Log.e(TAG, "    🏷️ ${desc.uuid}")
            }
            notifyError("未找到必需的配置描述符，无法启用通知")
            return false
        }

        Log.d(TAG, "✅ 找到配置描述符，准备写入通知配置...")

        // 根据特征值支持的功能选择合适的配置值
        val configValue = if (supportsNotify) {
            BluetoothGattDescriptor.ENABLE_NOTIFICATION_VALUE
        } else {
            BluetoothGattDescriptor.ENABLE_INDICATION_VALUE
        }

        descriptor.value = configValue
        val writeSuccess = gatt.writeDescriptor(descriptor)

        if (writeSuccess) {
            Log.d(TAG, "✅ 描述符写入请求已发送，等待写入完成...")
            // 注意：实际的成功/失败会在onDescriptorWrite回调中处理
            return true
        } else {
            val error = "❌ 描述符写入请求失败"
            Log.e(TAG, error)
            notifyError("启动通知订阅失败")
            return false
        }
    }
    
    /**
     * 向BLE设备的特征值写入数据
     */
    fun writeCharacteristic(deviceAddress: String, characteristicUuid: UUID, data: ByteArray): Boolean {
        Log.d(TAG, "📤 开始写入特征值: $deviceAddress, UUID: $characteristicUuid")
        Log.d(TAG, "📤 写入数据: ${data.joinToString(" ") { "%02X".format(it) }}")

        val gatt = gattConnections[deviceAddress]
        if (gatt == null) {
            val error = "❌ 设备未连接: $deviceAddress"
            Log.e(TAG, error)
            notifyError(error)
            return false
        }

        // 查找目标特征值
        var targetCharacteristic: BluetoothGattCharacteristic? = null
        var targetService: BluetoothGattService? = null

        for (service in gatt.services) {
            for (characteristic in service.characteristics) {
                if (characteristic.uuid == characteristicUuid) {
                    targetCharacteristic = characteristic
                    targetService = service
                    Log.d(TAG, "🎯 找到目标特征值: ${characteristic.uuid} (在服务 ${service.uuid} 中)")
                    break
                }
            }
            if (targetCharacteristic != null) break
        }

        if (targetCharacteristic == null) {
            val error = "❌ 未找到特征值: $characteristicUuid"
            Log.e(TAG, error)
            notifyError("未找到特征值 $characteristicUuid，请检查设备兼容性")
            return false
        }

        // 检查特征值是否支持写入
        val properties = targetCharacteristic.properties
        val supportsWrite = (properties and BluetoothGattCharacteristic.PROPERTY_WRITE) != 0
        val supportsWriteNoResponse = (properties and BluetoothGattCharacteristic.PROPERTY_WRITE_NO_RESPONSE) != 0

        if (!supportsWrite && !supportsWriteNoResponse) {
            val error = "❌ 特征值不支持写入功能 (属性: $properties)"
            Log.e(TAG, error)
            notifyError("特征值不支持写入功能")
            return false
        }

        Log.d(TAG, "✅ 特征值支持${if (supportsWrite) "写入" else ""}${if (supportsWriteNoResponse) "无响应写入" else ""}")

        // 设置写入类型
        if (supportsWriteNoResponse) {
            targetCharacteristic.writeType = BluetoothGattCharacteristic.WRITE_TYPE_NO_RESPONSE
        } else {
            targetCharacteristic.writeType = BluetoothGattCharacteristic.WRITE_TYPE_DEFAULT
        }

        // 写入数据
        targetCharacteristic.value = data
        val writeSuccess = gatt.writeCharacteristic(targetCharacteristic)

        if (writeSuccess) {
            Log.d(TAG, "✅ 特征值写入请求已发送，等待写入完成...")
            return true
        } else {
            val error = "❌ 特征值写入请求失败"
            Log.e(TAG, error)
            notifyError("特征值写入失败")
            return false
        }
    }

    /**
     * 发送外设控制指令
     */
    fun sendPeripheralCommand(deviceAddress: String, deviceType: Byte, command: Byte): Boolean {
        val data = byteArrayOf(deviceType, command)
        Log.d(TAG, "🎮 发送外设控制指令: 设备类型=${String.format("%02X", deviceType)}, 指令=${String.format("%02X", command)}")
        return writeCharacteristic(deviceAddress, CHARACTERISTIC_FFE1_UUID, data)
    }

    /**
     * 控制无线充电器
     */
    fun controlWirelessCharger(deviceAddress: String, enable: Boolean): Boolean {
        val command = if (enable) 0x01.toByte() else 0x00.toByte()
        Log.d(TAG, "🔌 控制无线充电器: ${if (enable) "开启" else "关闭"}")
        return sendPeripheralCommand(deviceAddress, 0x02.toByte(), command)
    }

    /**
     * 控制主灯
     */
    fun controlMainLamp(deviceAddress: String, enable: Boolean): Boolean {
        val command = if (enable) 0x01.toByte() else 0x00.toByte()
        Log.d(TAG, "💡 控制主灯: ${if (enable) "开启" else "关闭"}")
        return sendPeripheralCommand(deviceAddress, 0x03.toByte(), command)
    }

    /**
     * 控制氛围灯
     */
    fun controlAmbientLight(deviceAddress: String, enable: Boolean): Boolean {
        val command = if (enable) 0x01.toByte() else 0x00.toByte()
        Log.d(TAG, "🌈 控制氛围灯: ${if (enable) "开启" else "关闭"}")
        return sendPeripheralCommand(deviceAddress, 0x04.toByte(), command)
    }

    /**
     * 控制香氛机
     */
    fun controlAromaDiffuser(deviceAddress: String, enable: Boolean): Boolean {
        val command = if (enable) 0x01.toByte() else 0x00.toByte()
        Log.d(TAG, "🌸 控制香氛机: ${if (enable) "开启" else "关闭"}")
        return sendPeripheralCommand(deviceAddress, 0x05.toByte(), command)
    }

    /**
     * 控制风扇
     */
    fun controlFan(deviceAddress: String, enable: Boolean): Boolean {
        val command = if (enable) 0x01.toByte() else 0x00.toByte()
        Log.d(TAG, "🌀 控制风扇: ${if (enable) "开启" else "关闭"}")
        return sendPeripheralCommand(deviceAddress, 0x06.toByte(), command)
    }

    /**
     * 查询外设状态
     */
    fun queryPeripheralStatus(deviceAddress: String, deviceType: Byte): Boolean {
        Log.d(TAG, "❓ 查询外设状态: 设备类型=${String.format("%02X", deviceType)}")
        return sendPeripheralCommand(deviceAddress, deviceType, 0x02.toByte())
    }

    /**
     * 订阅状态反馈通知 (0xFFE2)
     * 用于接收外设状态变化通知
     */
    fun subscribeToStatusNotification(deviceAddress: String): Boolean {
        Log.d(TAG, "📱 订阅状态反馈通知: $deviceAddress")
        return subscribeToCharacteristic(deviceAddress, CHARACTERISTIC_FFE2_UUID)
    }

    /**
     * 订阅按键通知 (0xFFE4)
     * 用于接收按键设备的按键事件
     */
    fun subscribeToButtonNotification(deviceAddress: String): Boolean {
        Log.d(TAG, "📱 订阅按键通知: $deviceAddress")
        return subscribeToCharacteristic(deviceAddress, CHARACTERISTIC_FFE4_UUID)
    }

    /**
     * 断开BLE设备连接
     */
    fun disconnectBleDevice(deviceAddress: String) {
        val gatt = gattConnections[deviceAddress]
        gatt?.disconnect()
        clearConnectionTimeout(deviceAddress)
    }

    /**
     * 清除连接超时任务
     */
    private fun clearConnectionTimeout(deviceAddress: String) {
        connectionTimeouts[deviceAddress]?.let { timeoutTask ->
            handler.removeCallbacks(timeoutTask)
            connectionTimeouts.remove(deviceAddress)
            Log.d(TAG, "已清除连接超时任务: $deviceAddress")
        }
    }

    /**
     * 根据绑定状态智能处理服务发现
     * 这是解决Android BLE服务发现时序问题的关键方法
     */
    private fun handleServiceDiscoveryAfterConnection(gatt: BluetoothGatt, deviceAddress: String) {
        try {
            val device = bluetoothAdapter?.getRemoteDevice(deviceAddress)
            val bondState = device?.bondState ?: BluetoothDevice.BOND_NONE

            Log.d(TAG, "🔍 设备绑定状态: $bondState (设备: $deviceAddress)")

            when (bondState) {
                BluetoothDevice.BOND_NONE -> {
                    // 未绑定设备，可以立即发现服务
                    Log.d(TAG, "未绑定设备，立即发现服务")
                    performServiceDiscovery(gatt, deviceAddress, 0)
                }

                BluetoothDevice.BOND_BONDING -> {
                    // 正在绑定中，等待绑定完成
                    Log.i(TAG, "⏳ 设备正在绑定中，等待绑定完成后再发现服务")
                    handler.post {
                        notifyInfo("设备正在绑定中，请等待...")
                    }
                    // 注意：绑定完成后会触发广播，我们需要在那里处理服务发现
                }

                BluetoothDevice.BOND_BONDED -> {
                    // 已绑定设备，需要根据Android版本决定延迟时间
                    val delay = if (android.os.Build.VERSION.SDK_INT <= android.os.Build.VERSION_CODES.N) {
                        // Android 7及以下，需要1500ms延迟
                        1500L
                    } else {
                        // Android 8+，可以立即发现服务，但为了保险起见还是延迟一点
                        800L
                    }

                    Log.d(TAG, "🔗 已绑定设备，延迟${delay}ms后发现服务 (Android ${android.os.Build.VERSION.SDK_INT})")
                    performServiceDiscovery(gatt, deviceAddress, delay)
                }

                else -> {
                    Log.w(TAG, "⚠️ 未知绑定状态: $bondState，使用默认延迟")
                    performServiceDiscovery(gatt, deviceAddress, 1000L)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "处理服务发现时出错: ${e.message}")
            // 出错时使用保守的延迟策略
            performServiceDiscovery(gatt, deviceAddress, 1500L)
        }
    }

    /**
     * 执行服务发现
     */
    private fun performServiceDiscovery(gatt: BluetoothGatt, deviceAddress: String, delayMs: Long) {
        if (delayMs > 0) {
            Log.d(TAG, "⏰ 延迟${delayMs}ms后开始服务发现: $deviceAddress")
            handler.post {
                notifyInfo("等待${delayMs}ms后开始服务发现...")
            }
        }

        handler.postDelayed({
            Log.d(TAG, "🔍 开始服务发现: $deviceAddress")
            val success = gatt.discoverServices()
            if (success) {
                Log.d(TAG, "✅ 服务发现请求已发送: $deviceAddress")
                handler.post {
                    notifyInfo("正在发现设备服务...")
                }
            } else {
                Log.e(TAG, "❌ 服务发现请求失败: $deviceAddress")
                handler.post {
                    notifyError("服务发现请求失败，请重试连接")
                }
                // 服务发现失败，断开连接
                gatt.disconnect()
            }
        }, delayMs)
    }
    
    /**
     * 获取已连接的BLE设备列表
     */
    fun getConnectedBleDevices(): List<String> {
        return gattConnections.keys.toList()
    }

    /**
     * 改进的BLE连接方法，包含连接前准备
     */
    fun connectToBleDeviceWithPreparation(deviceAddress: String): Boolean {
        Log.d(TAG, "🚀 开始改进的BLE连接流程: $deviceAddress")

        // 步骤1: 停止BLE扫描（如果正在扫描）
        try {
            if (isScanning) {
                Log.d(TAG, "停止BLE扫描以提高连接成功率")
                stopBleScan()
                Thread.sleep(500) // 等待扫描完全停止
            }
        } catch (e: Exception) {
            Log.w(TAG, "停止扫描时出错: ${e.message}")
        }

        // 步骤2: 清理可能存在的旧连接
        val existingGatt = gattConnections[deviceAddress]
        if (existingGatt != null) {
            Log.d(TAG, "清理现有连接: $deviceAddress")
            existingGatt.disconnect()
            existingGatt.close()
            gattConnections.remove(deviceAddress)
            connectionCallbacks.remove(deviceAddress)
            clearConnectionTimeout(deviceAddress)

            // 等待清理完成
            Thread.sleep(1000)
        }

        // 步骤3: 执行连接
        return connectToBleDevice(deviceAddress)
    }

    /**
     * 连接问题诊断方法
     */
    fun diagnoseConnectionIssues(deviceAddress: String) {
        Log.d(TAG, "🔍 开始BLE连接问题诊断: $deviceAddress")
        handler.post {
            notifyInfo("开始BLE连接诊断...")
        }

        // 检查1: 蓝牙适配器状态
        if (bluetoothAdapter == null) {
            Log.e(TAG, "❌ 蓝牙适配器不可用")
            handler.post { notifyError("诊断结果：蓝牙适配器不可用") }
            return
        }

        if (!bluetoothAdapter.isEnabled) {
            Log.e(TAG, "❌ 蓝牙未启用")
            handler.post { notifyError("诊断结果：蓝牙未启用") }
            return
        }

        // 检查2: 设备地址格式
        if (!BluetoothAdapter.checkBluetoothAddress(deviceAddress)) {
            Log.e(TAG, "❌ 设备地址格式无效: $deviceAddress")
            handler.post { notifyError("诊断结果：设备地址格式无效") }
            return
        }

        // 检查3: 当前连接状态
        val existingGatt = gattConnections[deviceAddress]
        if (existingGatt != null) {
            try {
                val device = bluetoothAdapter.getRemoteDevice(deviceAddress)
                val connectionState = bluetoothManager?.getConnectionState(device!!, BluetoothProfile.GATT) ?: BluetoothProfile.STATE_DISCONNECTED
                Log.d(TAG, "📊 当前连接状态: $connectionState")

                val stateText = when (connectionState) {
                    BluetoothProfile.STATE_CONNECTED -> "已连接"
                    BluetoothProfile.STATE_CONNECTING -> "正在连接"
                    BluetoothProfile.STATE_DISCONNECTED -> "已断开"
                    BluetoothProfile.STATE_DISCONNECTING -> "正在断开"
                    else -> "未知状态($connectionState)"
                }

                handler.post {
                    notifyInfo("当前连接状态: $stateText")
                }
            } catch (e: Exception) {
                Log.e(TAG, "检查连接状态失败: ${e.message}")
            }
        }

        // 检查4: 重试历史
        val retryCount = connectionRetryCount[deviceAddress] ?: 0
        if (retryCount > 0) {
            Log.d(TAG, "📈 连接重试次数: $retryCount")
            handler.post {
                notifyInfo("已重试连接 $retryCount 次")
            }
        }

        // 提供建议
        handler.post {
            val suggestions = mutableListOf<String>()
            suggestions.add("1. 确保设备在可连接状态")
            suggestions.add("2. 靠近设备减少信号干扰")
            suggestions.add("3. 重启设备蓝牙功能")
            suggestions.add("4. 检查设备是否被其他应用占用")

            notifyInfo("连接建议：\n${suggestions.joinToString("\n")}")
        }

        Log.d(TAG, "🔍 BLE连接诊断完成")
    }

    /**
     * 诊断BLE设备的详细信息
     */
    fun diagnoseBleDevice(deviceAddress: String): Boolean {
        val gatt = gattConnections[deviceAddress]
        if (gatt == null) {
            Log.e(TAG, "设备未连接，无法诊断: $deviceAddress")
            notifyError("设备未连接，无法诊断: $deviceAddress")
            return false
        }

        Log.d(TAG, "🔍 开始诊断点云灵动键设备: $deviceAddress")
        Log.d(TAG, "====================================================")

        var serviceCount = 0
        var characteristicCount = 0
        var ffe0ServiceFound = false // 主服务
        var ffe1Found = false // 验证特征值
        var ffe4Found = false // 按键通知特征值
        var ffe5Found = false // 设备信息特征值

        for (service in gatt.services) {
            serviceCount++
            val serviceUuid = service.uuid.toString().uppercase()
            Log.d(TAG, "📋 服务 #$serviceCount: $serviceUuid")
            Log.d(TAG, "   类型: ${if (service.type == BluetoothGattService.SERVICE_TYPE_PRIMARY) "主服务" else "次服务"}")

            // 检查是否是0xFFE0主服务
            if (serviceUuid.contains("FFE0")) {
                ffe0ServiceFound = true
                Log.d(TAG, "   🎯 找到0xFFE0主服务!")
            }

            for (characteristic in service.characteristics) {
                characteristicCount++
                val uuid = characteristic.uuid.toString().uppercase()
                val properties = characteristic.properties

                Log.d(TAG, "   📄 特征值 #$characteristicCount: $uuid")
                Log.d(TAG, "      属性值: $properties")

                // 解析属性
                val propertyList = mutableListOf<String>()
                if (properties and BluetoothGattCharacteristic.PROPERTY_READ != 0) propertyList.add("READ")
                if (properties and BluetoothGattCharacteristic.PROPERTY_WRITE != 0) propertyList.add("WRITE")
                if (properties and BluetoothGattCharacteristic.PROPERTY_WRITE_NO_RESPONSE != 0) propertyList.add("WRITE_NO_RESPONSE")
                if (properties and BluetoothGattCharacteristic.PROPERTY_NOTIFY != 0) propertyList.add("NOTIFY")
                if (properties and BluetoothGattCharacteristic.PROPERTY_INDICATE != 0) propertyList.add("INDICATE")

                Log.d(TAG, "      支持操作: ${propertyList.joinToString(", ")}")

                // 检查是否是0xFFE4特征值
                if (uuid.contains("FFE4")) {
                    ffe4Found = true
                    Log.d(TAG, "      🎯 找到0xFFE4特征值!")

                    // 检查描述符
                    val descriptors = characteristic.descriptors
                    Log.d(TAG, "      描述符数量: ${descriptors.size}")
                    for (descriptor in descriptors) {
                        Log.d(TAG, "        📝 描述符: ${descriptor.uuid}")
                        if (descriptor.uuid.toString().uppercase().contains("2902")) {
                            Log.d(TAG, "        🎯 找到客户端特征配置描述符!")
                        }
                    }
                }
            }
        }

        Log.d(TAG, "====================================================")
        Log.d(TAG, "📊 点云灵动键诊断总结:")
        Log.d(TAG, "   服务总数: $serviceCount")
        Log.d(TAG, "   特征值总数: $characteristicCount")
        Log.d(TAG, "   0xFFE0 (主服务): ${if (ffe0ServiceFound) "✅ 找到" else "❌ 未找到"}")
        Log.d(TAG, "   0xFFE1 (验证): ${if (ffe1Found) "✅ 找到" else "❌ 未找到"}")
        Log.d(TAG, "   0xFFE4 (按键): ${if (ffe4Found) "✅ 找到" else "❌ 未找到"}")
        Log.d(TAG, "   0xFFE5 (信息): ${if (ffe5Found) "✅ 找到" else "❌ 未找到"}")

        // 关键：必须有0xFFE0服务和0xFFE4特征值才能正常通讯
        val isCompatible = ffe0ServiceFound && ffe4Found
        Log.d(TAG, "   设备兼容性: ${if (isCompatible) "✅ 兼容" else "❌ 不兼容"}")

        if (!ffe0ServiceFound) {
            Log.e(TAG, "   ❌ 缺少0xFFE0主服务，无法建立通讯")
        }
        if (!ffe4Found) {
            Log.e(TAG, "   ❌ 缺少0xFFE4按键通知特征值")
        }

        Log.d(TAG, "====================================================")

        handler.post {
            if (isCompatible) {
                val features = mutableListOf<String>()
                if (ffe0ServiceFound) features.add("主服务")
                if (ffe1Found) features.add("设备验证")
                if (ffe4Found) features.add("按键通知")
                if (ffe5Found) features.add("设备信息")

                notifySuccess("点云灵动键诊断完成: 设备完全兼容，支持功能: ${features.joinToString(", ")}")
            } else {
                val missing = mutableListOf<String>()
                if (!ffe0ServiceFound) missing.add("0xFFE0主服务")
                if (!ffe4Found) missing.add("0xFFE4按键通知")

                notifyError("诊断完成: 设备不兼容，缺少关键组件: ${missing.joinToString(", ")}")
            }
        }

        return isCompatible
    }
    
    // 通知方法
    private fun notifyDeviceFound(device: BluetoothDeviceInfo) {
        callbacks.forEach { it.onDeviceFound(device) }
    }
    
    private fun notifyError(error: String) {
        callbacks.forEach { it.onError(error) }
    }
    
    private fun notifySuccess(message: String) {
        callbacks.forEach { it.onSuccess(message) }
    }
    
    private fun notifyInfo(message: String) {
        callbacks.forEach { it.onInfo(message) }
    }

    private fun notifyWarning(message: String) {
        callbacks.forEach { it.onInfo("⚠️ $message") }  // 使用Info回调但添加警告图标
    }
    
    private fun notifyCharacteristicNotification(deviceAddress: String, characteristicUuid: String, data: ByteArray) {
        callbacks.forEach { it.onCharacteristicNotification(deviceAddress, characteristicUuid, data) }
    }
    
    private fun notifyBleConnectionStateChanged(deviceAddress: String, state: Int) {
        callbacks.forEach { it.onBleConnectionStateChanged(deviceAddress, state) }
    }
    
    /**
     * 初始化点云灵动键设备
     * 关键：确保连接到0xFFE0服务才能正常通讯
     */
    private fun initializeDotixDevice(deviceAddress: String) {
        Log.d(TAG, "🚀 开始初始化点云灵动键: $deviceAddress")

        val gatt = gattConnections[deviceAddress]
        if (gatt == null) {
            Log.e(TAG, "设备未连接，无法初始化: $deviceAddress")
            return
        }

        // 关键步骤：确保连接到0xFFE0服务
        val ffe0Service = gatt.getService(SERVICE_FFE0_UUID)
        if (ffe0Service == null) {
            Log.e(TAG, "❌ 未找到0xFFE0服务，设备可能不兼容")
            handler.post {
                notifyError("未找到0xFFE0服务，无法建立通讯")
            }
            return
        }

        Log.d(TAG, "✅ 找到0xFFE0服务，开始初始化通讯")

        // 步骤1: 订阅按键通知 (0xFFE4)
        handler.postDelayed({
            Log.d(TAG, "📱 订阅0xFFE4按键通知特征值")
            subscribeToFFE4Characteristic(deviceAddress, ffe0Service)
        }, 500)

        // 步骤2: 订阅状态反馈通知 (0xFFE2)
        handler.postDelayed({
            Log.d(TAG, "📱 订阅0xFFE2状态反馈特征值")
            subscribeToFFE2Characteristic(deviceAddress, ffe0Service)
        }, 800)

        // 步骤3: 读取设备基本信息 (0xFFE5)
        handler.postDelayed({
            readDeviceInfo(deviceAddress, ffe0Service)
        }, 1200)
    }

    /**
     * 专门订阅0xFFE4特征值的方法
     * 确保在正确的服务下进行订阅
     * 基于客户提供的工作代码实现，包含关键的100ms等待
     */
    private fun subscribeToFFE4Characteristic(deviceAddress: String, ffe0Service: BluetoothGattService) {
        val ffe4Characteristic = ffe0Service.getCharacteristic(CHARACTERISTIC_FFE4_UUID)
        if (ffe4Characteristic == null) {
            Log.e(TAG, "❌ 在0xFFE0服务中未找到0xFFE4特征值")
            handler.post {
                notifyError("未找到0xFFE4按键通知特征值")
            }
            return
        }

        Log.d(TAG, "✅ 在0xFFE0服务中找到0xFFE4特征值")

        val gatt = gattConnections[deviceAddress]
        if (gatt == null) {
            Log.e(TAG, "设备连接已断开: $deviceAddress")
            return
        }

        // 步骤1: 启用通知
        val success = gatt.setCharacteristicNotification(ffe4Characteristic, true)
        if (!success) {
            Log.e(TAG, "❌ 启用0xFFE4特征值通知失败")
            handler.post {
                notifyError("启用按键通知失败")
            }
            return
        }

        Log.d(TAG, "✅ 0xFFE4特征值通知已启用")

        // 步骤2: 关键等待100ms（参考客户提供的工作代码）
        handler.postDelayed({
            Log.d(TAG, "⏰ 修改Characteristic Notification后，等待100ms完成")

            // 步骤3: 写入客户端特征配置描述符
            val descriptor = ffe4Characteristic.getDescriptor(CLIENT_CHARACTERISTIC_CONFIG_UUID)
            if (descriptor != null) {
                Log.d(TAG, "📝 写入客户端特征配置描述符")
                descriptor.value = BluetoothGattDescriptor.ENABLE_NOTIFICATION_VALUE
                val writeSuccess = gatt.writeDescriptor(descriptor)
                if (writeSuccess) {
                    Log.d(TAG, "✅ 描述符写入请求已发送")
                    handler.post {
                        notifySuccess("点云灵动键按键通知订阅成功，可以开始按键测试")
                    }
                } else {
                    Log.e(TAG, "❌ 描述符写入请求失败")
                    handler.post {
                        notifyError("订阅按键通知失败")
                    }
                }
            } else {
                Log.e(TAG, "❌ 未找到客户端特征配置描述符")
                handler.post {
                    notifyError("设备不支持通知订阅")
                }
            }
        }, 100) // 关键：等待100ms，参考客户代码
    }

    /**
     * 专门订阅0xFFE2状态反馈特征值的方法
     * 用于接收外设状态变化通知
     */
    private fun subscribeToFFE2Characteristic(deviceAddress: String, ffe0Service: BluetoothGattService) {
        val ffe2Characteristic = ffe0Service.getCharacteristic(CHARACTERISTIC_FFE2_UUID)
        if (ffe2Characteristic == null) {
            Log.w(TAG, "⚠️ 在0xFFE0服务中未找到0xFFE2状态特征值")
            handler.post {
                notifyWarning("未找到0xFFE2状态反馈特征值，无法接收状态通知")
            }
            return
        }

        Log.d(TAG, "✅ 在0xFFE0服务中找到0xFFE2状态特征值")

        val gatt = gattConnections[deviceAddress]
        if (gatt == null) {
            Log.e(TAG, "设备连接已断开: $deviceAddress")
            return
        }

        // 检查特征值是否支持通知
        val properties = ffe2Characteristic.properties
        val supportsNotify = (properties and BluetoothGattCharacteristic.PROPERTY_NOTIFY) != 0

        if (!supportsNotify) {
            Log.w(TAG, "⚠️ 0xFFE2特征值不支持通知功能")
            handler.post {
                notifyWarning("0xFFE2特征值不支持通知，将无法接收状态变化")
            }
            return
        }

        // 步骤1: 启用通知
        val success = gatt.setCharacteristicNotification(ffe2Characteristic, true)
        if (!success) {
            Log.e(TAG, "❌ 启用0xFFE2特征值通知失败")
            handler.post {
                notifyError("启用状态反馈通知失败")
            }
            return
        }

        Log.d(TAG, "✅ 0xFFE2特征值通知已启用")

        // 步骤2: 等待100ms后写入描述符
        handler.postDelayed({
            Log.d(TAG, "⏰ 修改0xFFE2 Characteristic Notification后，等待100ms完成")

            // 步骤3: 写入客户端特征配置描述符
            val descriptor = ffe2Characteristic.getDescriptor(CLIENT_CHARACTERISTIC_CONFIG_UUID)
            if (descriptor != null) {
                Log.d(TAG, "📝 写入0xFFE2客户端特征配置描述符")
                descriptor.value = BluetoothGattDescriptor.ENABLE_NOTIFICATION_VALUE
                val writeSuccess = gatt.writeDescriptor(descriptor)
                if (writeSuccess) {
                    Log.d(TAG, "✅ 0xFFE2描述符写入请求已发送")
                    handler.post {
                        notifySuccess("状态反馈通知订阅成功，可以接收设备状态变化")
                    }
                } else {
                    Log.e(TAG, "❌ 0xFFE2描述符写入请求失败")
                    handler.post {
                        notifyError("订阅状态反馈通知失败")
                    }
                }
            } else {
                Log.e(TAG, "❌ 0xFFE2未找到客户端特征配置描述符")
                handler.post {
                    notifyError("0xFFE2设备不支持通知订阅")
                }
            }
        }, 100) // 等待100ms
    }

    /**
     * 读取设备基本信息 (0xFFE5)
     */
    private fun readDeviceInfo(deviceAddress: String, ffe0Service: BluetoothGattService) {
        val ffe5Characteristic = ffe0Service.getCharacteristic(CHARACTERISTIC_FFE5_UUID)
        if (ffe5Characteristic == null) {
            Log.w(TAG, "未找到0xFFE5设备信息特征值")
            return
        }

        val gatt = gattConnections[deviceAddress]
        if (gatt == null) {
            Log.e(TAG, "设备连接已断开: $deviceAddress")
            return
        }

        Log.d(TAG, "📖 读取设备基本信息")
        val success = gatt.readCharacteristic(ffe5Characteristic)
        if (success) {
            Log.d(TAG, "设备信息读取请求已发送")
        } else {
            Log.e(TAG, "设备信息读取请求失败")
        }
    }

    /**
     * 解析按键数据
     * 根据协议格式：按键ID + 动作类型 + 其他数据
     * - 动作类型 0x01: 短按
     * - 动作类型 0x02: 长按 (200ms间隔重复发送)
     * - 动作类型 0x03: 特殊状态（如低电量）
     */
    private fun parseButtonData(data: ByteArray): String {
        if (data.size < 2) {
            return "数据格式错误"
        }

        val buttonId = data[0].toInt() and 0xFF      // 第一个字节是按键ID
        val actionType = data[1].toInt() and 0xFF    // 第二个字节是动作类型

        return when (actionType) {
            0x01 -> "短按按键 #$buttonId"
            0x02 -> "长按按键 #$buttonId"
            0x03 -> if (buttonId == 0x01) "低电量警告" else "特殊状态: 按键#$buttonId"
            else -> "未知动作: 按键#$buttonId, 动作类型: 0x${String.format("%02X", actionType)}"
        }
    }

    /**
     * 解析状态反馈数据
     * 根据协议格式：设备类型 + 状态码 + 数值
     * - 状态码 0x00: 关闭/待机
     * - 状态码 0x01: 开启/工作中
     * - 状态码 0xFF: 异常/错误
     */
    private fun parseStatusData(data: ByteArray): String {
        if (data.size < 2) {
            return "状态数据格式错误"
        }

        val deviceType = data[0].toInt() and 0xFF    // 第一个字节是设备类型
        val statusCode = data[1].toInt() and 0xFF    // 第二个字节是状态码

        val deviceName = when (deviceType) {
            0x02 -> "无线充电器"
            0x03 -> "主灯"
            0x04 -> "氛围灯"
            0x05 -> "香氛机"
            0x06 -> "风扇"
            else -> "未知设备(0x${String.format("%02X", deviceType)})"
        }

        val statusText = when (statusCode) {
            0x00 -> "关闭/待机"
            0x01 -> "开启/工作中"
            0xFF -> "异常/错误"
            else -> "未知状态(0x${String.format("%02X", statusCode)})"
        }

        return "$deviceName: $statusText"
    }

    /**
     * 启动连接保活机制
     * 通过定期读取设备信息来维持连接活跃状态
     */
    private fun startConnectionKeepalive(deviceAddress: String) {
        Log.d(TAG, "🔄 启动连接保活机制: $deviceAddress")

        // 清理可能存在的旧保活任务
        stopConnectionKeepalive(deviceAddress)

        // 初始化保活状态
        lastKeepaliveTime[deviceAddress] = System.currentTimeMillis()
        missedKeepaliveCount[deviceAddress] = 0

        // 启动保活定时器
        val keepaliveTask = object : Runnable {
            override fun run() {
                performKeepalive(deviceAddress)
                // 继续下一次保活
                keepaliveTimers[deviceAddress] = this
                handler.postDelayed(this, KEEPALIVE_INTERVAL_MS)
            }
        }
        keepaliveTimers[deviceAddress] = keepaliveTask
        handler.postDelayed(keepaliveTask, KEEPALIVE_INTERVAL_MS)

        // 启动连接健康检查
        val healthCheckTask = object : Runnable {
            override fun run() {
                checkConnectionHealth(deviceAddress)
                // 继续下一次检查
                connectionHealthCheck[deviceAddress] = this
                handler.postDelayed(this, CONNECTION_CHECK_INTERVAL_MS)
            }
        }
        connectionHealthCheck[deviceAddress] = healthCheckTask
        handler.postDelayed(healthCheckTask, CONNECTION_CHECK_INTERVAL_MS)

        Log.d(TAG, "✅ 连接保活机制已启动: $deviceAddress")
    }

    /**
     * 停止连接保活机制
     */
    private fun stopConnectionKeepalive(deviceAddress: String) {
        Log.d(TAG, "🛑 停止连接保活机制: $deviceAddress")

        // 停止保活定时器
        keepaliveTimers[deviceAddress]?.let { task ->
            handler.removeCallbacks(task)
            keepaliveTimers.remove(deviceAddress)
        }

        // 停止健康检查
        connectionHealthCheck[deviceAddress]?.let { task ->
            handler.removeCallbacks(task)
            connectionHealthCheck.remove(deviceAddress)
        }

        // 清理保活状态
        lastKeepaliveTime.remove(deviceAddress)
        missedKeepaliveCount.remove(deviceAddress)

        Log.d(TAG, "✅ 连接保活机制已停止: $deviceAddress")
    }

    /**
     * 执行保活操作
     * 通过读取设备信息特征值来维持连接活跃
     */
    private fun performKeepalive(deviceAddress: String) {
        val gatt = gattConnections[deviceAddress]
        if (gatt == null) {
            Log.w(TAG, "⚠️ 保活失败：GATT连接不存在: $deviceAddress")
            stopConnectionKeepalive(deviceAddress)
            return
        }

        Log.d(TAG, "💓 执行连接保活: $deviceAddress")

        try {
            // 查找0xFFE0服务
            val ffe0Service = gatt.services?.find {
                it.uuid.toString().uppercase().contains("FFE0")
            }

            if (ffe0Service != null) {
                // 尝试读取0xFFE5设备信息特征值作为保活
                val ffe5Characteristic = ffe0Service.getCharacteristic(CHARACTERISTIC_FFE5_UUID)
                if (ffe5Characteristic != null &&
                    (ffe5Characteristic.properties and BluetoothGattCharacteristic.PROPERTY_READ) != 0) {

                    Log.d(TAG, "📖 读取设备信息特征值作为保活: $deviceAddress")
                    val success = gatt.readCharacteristic(ffe5Characteristic)
                    if (success) {
                        lastKeepaliveTime[deviceAddress] = System.currentTimeMillis()
                        missedKeepaliveCount[deviceAddress] = 0
                        Log.d(TAG, "✅ 保活请求已发送: $deviceAddress")
                    } else {
                        Log.w(TAG, "⚠️ 保活请求发送失败: $deviceAddress")
                        handleKeepaliveFailed(deviceAddress)
                    }
                } else {
                    // 如果没有可读特征值，尝试请求MTU作为保活
                    Log.d(TAG, "📡 请求MTU作为保活: $deviceAddress")
                    val success = gatt.requestMtu(23) // 请求最小MTU
                    if (success) {
                        lastKeepaliveTime[deviceAddress] = System.currentTimeMillis()
                        missedKeepaliveCount[deviceAddress] = 0
                        Log.d(TAG, "✅ MTU保活请求已发送: $deviceAddress")
                    } else {
                        Log.w(TAG, "⚠️ MTU保活请求发送失败: $deviceAddress")
                        handleKeepaliveFailed(deviceAddress)
                    }
                }
            } else {
                Log.w(TAG, "⚠️ 未找到0xFFE0服务，无法执行保活: $deviceAddress")
                handleKeepaliveFailed(deviceAddress)
            }
        } catch (e: Exception) {
            Log.e(TAG, "❌ 保活操作异常: $deviceAddress, ${e.message}")
            handleKeepaliveFailed(deviceAddress)
        }
    }

    /**
     * 检查连接健康状态
     */
    private fun checkConnectionHealth(deviceAddress: String) {
        val gatt = gattConnections[deviceAddress]
        if (gatt == null) {
            Log.w(TAG, "⚠️ 连接健康检查：GATT连接不存在: $deviceAddress")
            stopConnectionKeepalive(deviceAddress)
            return
        }

        val lastKeepalive = lastKeepaliveTime[deviceAddress] ?: 0
        val currentTime = System.currentTimeMillis()
        val timeSinceLastKeepalive = currentTime - lastKeepalive

        Log.d(TAG, "🔍 连接健康检查: $deviceAddress, 距离上次保活: ${timeSinceLastKeepalive}ms")

        // 检查是否超过保活间隔太久
        if (timeSinceLastKeepalive > KEEPALIVE_INTERVAL_MS * 2) {
            val missedCount = missedKeepaliveCount[deviceAddress] ?: 0
            Log.w(TAG, "⚠️ 连接可能不稳定: $deviceAddress, 错过保活次数: $missedCount")

            if (missedCount >= MAX_MISSED_KEEPALIVE) {
                Log.e(TAG, "❌ 连接已失效，主动断开: $deviceAddress")
                handler.post {
                    notifyError("连接超时：设备长时间无响应，已主动断开连接")
                }
                // 主动断开连接
                gatt.disconnect()
            }
        }
    }

    /**
     * 处理保活失败
     */
    private fun handleKeepaliveFailed(deviceAddress: String) {
        val missedCount = missedKeepaliveCount.getOrDefault(deviceAddress, 0) + 1
        missedKeepaliveCount[deviceAddress] = missedCount

        Log.w(TAG, "⚠️ 保活失败: $deviceAddress, 连续失败次数: $missedCount")

        if (missedCount >= MAX_MISSED_KEEPALIVE) {
            Log.e(TAG, "❌ 保活连续失败${MAX_MISSED_KEEPALIVE}次，连接可能已断开: $deviceAddress")
            handler.post {
                notifyError("连接不稳定：保活失败，可能需要重新连接")
            }
        }
    }

    /**
     * 清理资源
     */
    fun cleanup() {
        stopBleScan()

        // 停止所有保活机制
        gattConnections.keys.forEach { deviceAddress ->
            stopConnectionKeepalive(deviceAddress)
        }

        gattConnections.values.forEach { it.close() }
        gattConnections.clear()
        connectionCallbacks.clear()
        connectionRetryCount.clear()
        connectionTimeouts.clear()

        // 清理保活相关资源
        keepaliveTimers.clear()
        connectionHealthCheck.clear()
        lastKeepaliveTime.clear()
        missedKeepaliveCount.clear()
    }
}
