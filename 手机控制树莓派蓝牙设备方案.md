# 📱 手机控制树莓派蓝牙设备方案

## 🎯 需求概述

实现手机通过蓝牙控制树莓派的蓝牙扫描、连接、配对功能，并能查看和控制树莓派连接的外设设备。

## 🏗️ 系统架构

```
手机APP ←蓝牙SPP→ 树莓派 ←蓝牙BLE→ 外设(充电器/灯具等)
```

**通讯流程**：
1. 手机通过经典蓝牙SPP协议连接树莓派
2. 手机发送控制指令给树莓派
3. 树莓派执行蓝牙操作（扫描、连接外设等）
4. 树莓派通过BLE协议控制外设
5. 树莓派将结果反馈给手机

## 📋 通讯协议设计（重新设计）

### 基本原则
- **手机端**：只负责发送指令和显示结果，不控制扫描时长
- **树莓派端**：负责实际执行扫描，自主控制扫描时长，发送完整结果

### 指令格式
```
发送指令: [指令类型]:[操作码]:[参数...]
响应格式: [指令类型]:[状态码]:[数据...]
```

### 指令类型定义

#### 1. 树莓派蓝牙管理指令 (A0)
- `A0:01` - 开始BLE扫描（手机→树莓派）
- `A0:00` - 停止BLE扫描（手机→树莓派）

#### 2. 设备连接和外设控制指令 (A1)
- `A1:CONNECT:[设备地址]` - 连接指定设备（手机→树莓派）
- `A1:[设备类型]:[指令]:[设备地址]` - 控制外设（手机→树莓派）
- 示例:
  - `A1:CONNECT:AA:BB:CC:DD:EE:FF` (连接设备)
  - `A1:02:01:AA:BB:CC:DD:EE:FF` (开启充电器)

#### 3. 状态查询指令 (A2)
- `A2:00` - 查询所有已连接设备

### 响应格式

#### 扫描完成响应（新增）
```
A0:SCAN_COMPLETE:[设备数量]:[设备1信息];[设备2信息];...
设备信息格式: [设备类型|设备名|设备地址|信号强度]
```

#### 连接结果响应
```
A1:CONNECT:[设备地址]:OK/FAIL:[错误信息]
```

#### 设备列表响应
```
A2:00:[设备类型|设备名|设备地址];[设备类型|设备名|设备地址]...
```

#### 控制结果响应
```
A1:[设备类型]:[指令]:[设备地址]:OK/FAIL
```

## 🔧 实现方案（最小改动）

### 1. 手机端改动

#### 配置文件修改
在 `DeviceConfig.kt` 中新增功能开关：
```kotlin
val showRaspberryPiControl: Boolean = isPhoneVersion  // 显示树莓派控制功能
```

#### 新增UI组件
- `RaspberryPiControlSection` - 树莓派控制界面
- 包含设备选择、扫描控制、外设管理等功能

#### 响应处理
- `processRaspberryPiResponse()` - 解析树莓派响应
- 更新设备列表和状态

### 2. 树莓派端改动

#### 指令处理
在 `BluetoothManager.kt` 中新增：
- `processRaspberryPiCommand()` - 解析和执行控制指令
- `sendResponse()` - 发送响应给手机
- 支持BLE扫描、设备控制、状态查询

#### BLE设备管理
- 利用现有的 `BleManager` 进行设备操作
- 自动维护已连接设备列表
- 转发外设控制指令

## 📱 用户操作流程（重新设计）

### 1. 连接树莓派
1. 手机扫描并连接到树莓派设备
2. 在"树莓派控制中心"选择目标树莓派

### 2. 扫描BLE设备（新流程）
1. **手机操作**：点击"开始扫描"按钮
2. **手机发送**：发送 `A0:01` 指令给树莓派
3. **手机状态**：显示"正在扫描..."（无倒计时）
4. **树莓派执行**：收到指令后开始BLE扫描，自主控制扫描时长
5. **树莓派完成**：扫描完成后发送 `A0:SCAN_COMPLETE` 消息
6. **手机更新**：收到消息后停止扫描状态，显示设备列表

### 3. 连接设备
1. **手机操作**：点击设备列表中的设备
2. **手机发送**：发送 `A1:CONNECT:[设备地址]` 指令
3. **树莓派执行**：尝试连接指定设备
4. **树莓派反馈**：发送连接结果 `A1:CONNECT:[设备地址]:OK/FAIL`
5. **手机更新**：根据结果更新设备连接状态

### 4. 控制外设
1. 对已连接的外设进行开关控制
2. 实时显示外设的开关状态
3. 不同设备类型显示对应图标

## 🎨 UI设计

### 树莓派控制界面
```
🍓 树莓派控制中心
├── 选择树莓派设备 (FilterChip列表)
├── 蓝牙设备管理
│   ├── [开始扫描] [停止扫描] [刷新设备]
└── 已连接的外设
    ├── 🔌 无线充电器 [●开启] [关闭]
    ├── 💡 Lamp主灯    [○关闭] [开启]
    └── 🌈 氛围灯      [●开启] [关闭]
```

## 🔄 设备类型映射

| 设备类型 | 代码 | 图标 | 说明 |
|----------|------|------|------|
| 无线充电器 | 02 | 🔌 | 蓝牙无线充电设备 |
| Lamp主灯 | 03 | 💡 | 主要照明灯具 |
| 氛围灯 | 04 | 🌈 | 装饰性彩色灯具 |
| 香氛机 | 05 | 🌸 | 香氛扩散设备 |
| 风扇 | 06 | 🌀 | 电风扇设备 |

## ✅ 优势特点

### 1. 最小改动
- 复用现有蓝牙通讯框架
- 利用现有BLE管理功能
- 只新增必要的UI和指令处理

### 2. 协议简单
- 基于文本的简单协议
- 易于调试和扩展
- 向后兼容现有功能

### 3. 用户友好
- 直观的图形界面
- 实时状态反馈
- 一键式操作

### 4. 可扩展性
- 易于添加新的设备类型
- 支持更多控制指令
- 可扩展更复杂的状态查询

## 🚀 部署说明

### 手机版配置
```kotlin
private val DEVICE_TYPE = DeviceType.PHONE
```

### 树莓派版配置
```kotlin
private val DEVICE_TYPE = DeviceType.RASPBERRY_PI
```

部署后，手机版会显示"树莓派控制中心"，树莓派版会自动启动蓝牙服务器等待连接。

## 📝 注意事项

1. **权限要求**: 确保蓝牙权限已正确配置
2. **设备配对**: 手机和树莓派需要先完成蓝牙配对
3. **网络稳定**: 保持蓝牙连接稳定性
4. **错误处理**: 实现了基本的错误处理和重试机制
5. **状态同步**: 设备状态通过查询指令保持同步
